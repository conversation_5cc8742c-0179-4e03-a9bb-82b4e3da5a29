# Deployment Guide

## Production Deployment Options

### 1. Docker Deployment (Recommended)

#### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- Camera device access

#### Quick Deployment
```bash
# Clone repository
git clone <repository-url>
cd AI-TargetIdenfify

# Configure environment
cp .env.example .env
# Edit .env with production settings

# Deploy with Docker Compose
docker-compose up -d

# Check status
docker-compose ps
docker-compose logs conveyor-monitor
```

#### Production Docker Compose
```yaml
version: '3.8'
services:
  conveyor-monitor:
    build: .
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - /dev/video0:/dev/video0
    environment:
      - DEBUG=false
      - DATABASE_URL=************************************/conveyor_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    privileged: true

  postgres:
    image: postgres:15
    restart: unless-stopped
    environment:
      POSTGRES_DB: conveyor_db
      POSTGRES_USER: conveyor_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    volumes:
      - redis_data:/data

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - conveyor-monitor

volumes:
  postgres_data:
  redis_data:
```

### 2. Systemd Service (Linux)

#### Installation
```bash
# Run setup with service creation
python scripts/setup.py --service

# Enable and start service
sudo systemctl enable conveyor-monitor
sudo systemctl start conveyor-monitor

# Check status
sudo systemctl status conveyor-monitor
sudo journalctl -u conveyor-monitor -f
```

#### Service Configuration
```ini
[Unit]
Description=KR Conveyor Belt Target Identification System
After=network.target

[Service]
Type=simple
User=conveyor
WorkingDirectory=/opt/conveyor-monitor
Environment=PATH=/opt/conveyor-monitor/venv/bin
ExecStart=/opt/conveyor-monitor/venv/bin/python main.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 3. Kubernetes Deployment

#### Namespace and ConfigMap
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: conveyor-monitoring

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: conveyor-config
  namespace: conveyor-monitoring
data:
  .env: |
    DEBUG=false
    DATABASE_URL=************************************/conveyor_db
    REDIS_URL=redis://redis:6379
    MODEL_CONFIDENCE=0.5
    DETECTION_INTERVAL=1
```

#### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: conveyor-monitor
  namespace: conveyor-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: conveyor-monitor
  template:
    metadata:
      labels:
        app: conveyor-monitor
    spec:
      containers:
      - name: conveyor-monitor
        image: conveyor-monitor:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: conveyor-config
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: models-volume
          mountPath: /app/models
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: conveyor-data-pvc
      - name: models-volume
        persistentVolumeClaim:
          claimName: conveyor-models-pvc
```

## Environment Configuration

### Production Environment Variables
```bash
# Application
DEBUG=false
HOST=0.0.0.0
PORT=8000

# Security
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=your-domain.com,localhost

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/conveyor_db
REDIS_URL=redis://localhost:6379

# AI Model
MODEL_PATH=/app/models/production_model.pt
MODEL_CONFIDENCE=0.7
DEVICE=cuda

# Monitoring
DETECTION_INTERVAL=0.5
ALERT_THRESHOLD=0.8

# Email Alerts
EMAIL_ENABLED=true
EMAIL_SMTP_SERVER=smtp.company.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=secure-password

# Logging
LOG_LEVEL=INFO
LOG_FILE=/app/logs/production.log
```

### SSL/TLS Configuration

#### Nginx SSL Configuration
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    location / {
        proxy_pass http://conveyor-monitor:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## Database Setup

### PostgreSQL (Recommended for Production)
```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE conveyor_monitoring;
CREATE USER conveyor_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE conveyor_monitoring TO conveyor_user;
\q

# Update connection string
DATABASE_URL=postgresql://conveyor_user:secure_password@localhost:5432/conveyor_monitoring
```

### Database Migration
```bash
# Initialize database schema
python -c "
import asyncio
from core.database import DatabaseManager

async def init_db():
    db = DatabaseManager()
    await db.initialize()
    await db.close()

asyncio.run(init_db())
"
```

## Monitoring and Logging

### Log Configuration
```python
# Production logging setup
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'detailed': {
            'format': '{asctime} {name} {levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/app/logs/production.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/app/logs/error.log',
            'maxBytes': 10485760,
            'backupCount': 5,
            'formatter': 'detailed',
        },
    },
    'root': {
        'level': 'INFO',
        'handlers': ['file', 'error_file'],
    },
}
```

### Health Checks
```bash
# Application health check
curl -f http://localhost:8000/api/dashboard/system-health

# Docker health check
docker-compose exec conveyor-monitor curl -f http://localhost:8000/api/detection/status
```

### Monitoring with Prometheus
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'conveyor-monitor'
    static_configs:
      - targets: ['conveyor-monitor:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

## Security Considerations

### Network Security
- Use HTTPS in production
- Implement proper firewall rules
- Restrict API access to authorized networks
- Use VPN for remote access

### Application Security
- Enable authentication and authorization
- Implement rate limiting
- Validate all input data
- Use secure session management
- Regular security updates

### Data Protection
- Encrypt sensitive data at rest
- Secure database connections
- Implement audit logging
- Regular data backups
- GDPR compliance if applicable

## Backup and Recovery

### Database Backup
```bash
# PostgreSQL backup
pg_dump -h localhost -U conveyor_user conveyor_monitoring > backup_$(date +%Y%m%d).sql

# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U conveyor_user conveyor_monitoring | gzip > $BACKUP_DIR/conveyor_backup_$DATE.sql.gz

# Keep only last 30 days
find $BACKUP_DIR -name "conveyor_backup_*.sql.gz" -mtime +30 -delete
```

### Application Data Backup
```bash
# Backup models and configuration
tar -czf app_backup_$(date +%Y%m%d).tar.gz models/ data/ logs/ .env

# Restore from backup
tar -xzf app_backup_20231201.tar.gz
```

## Performance Optimization

### Hardware Recommendations
- **CPU**: 4+ cores, 2.5GHz+
- **RAM**: 8GB+ (16GB recommended)
- **Storage**: SSD for database and logs
- **GPU**: NVIDIA RTX series for CUDA acceleration
- **Network**: Gigabit Ethernet for IP cameras

### Application Tuning
```bash
# Environment variables for performance
DETECTION_INTERVAL=0.5  # Faster detection
MODEL_CONFIDENCE=0.6    # Balanced accuracy/speed
DEVICE=cuda            # GPU acceleration
IMAGE_WIDTH=416        # Smaller images for speed
IMAGE_HEIGHT=416
```

### Database Optimization
```sql
-- PostgreSQL optimization
CREATE INDEX idx_detection_timestamp ON detection_results(timestamp);
CREATE INDEX idx_alerts_type_timestamp ON alerts(alert_type, timestamp);

-- Vacuum and analyze regularly
VACUUM ANALYZE detection_results;
VACUUM ANALYZE alerts;
```

## Troubleshooting

### Common Production Issues

1. **High Memory Usage**
   - Monitor with `docker stats` or `htop`
   - Adjust model batch size
   - Implement memory limits

2. **Camera Connection Issues**
   - Check device permissions
   - Verify camera drivers
   - Test with `v4l2-ctl --list-devices`

3. **Database Connection Errors**
   - Check connection string
   - Verify database service status
   - Monitor connection pool

4. **Performance Degradation**
   - Monitor CPU/GPU usage
   - Check disk space
   - Review log files for errors

### Log Analysis
```bash
# Monitor application logs
tail -f /app/logs/production.log

# Search for errors
grep -i error /app/logs/production.log

# Monitor system resources
docker stats conveyor-monitor
```
