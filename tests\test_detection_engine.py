"""
Tests for Detection Engine
"""
import pytest
import numpy as np
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from core.detection_engine import DetectionEngine
from core.ai_model import AIModelManager, DetectionResult
from core.image_processor import ImageProcessor
from core.database import DatabaseManager


@pytest.fixture
def mock_ai_model():
    """Mock AI model for testing"""
    model = Mock(spec=AIModelManager)
    model.detect = AsyncMock()
    model.analyze_clearing_status = Mock()
    model.is_loaded = True
    return model


@pytest.fixture
def mock_image_processor():
    """Mock image processor for testing"""
    processor = Mock(spec=ImageProcessor)
    processor.initialize_camera = AsyncMock(return_value=True)
    processor.capture_frame = AsyncMock()
    processor.preprocess_image = AsyncMock()
    processor.load_image_from_file = AsyncMock()
    processor.load_image_from_bytes = AsyncMock()
    processor.draw_detections = Mock()
    processor.image_to_base64 = Mock(return_value="base64_image_data")
    processor.is_camera_active = True
    return processor


@pytest.fixture
def mock_database():
    """Mock database for testing"""
    db = Mock(spec=DatabaseManager)
    db.store_detection_result = AsyncMock()
    return db


@pytest.fixture
def detection_engine(mock_ai_model, mock_image_processor, mock_database):
    """Create detection engine for testing"""
    return DetectionEngine(mock_ai_model, mock_image_processor, mock_database)


@pytest.fixture
def sample_image():
    """Create sample image for testing"""
    return np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)


@pytest.fixture
def sample_detection_result():
    """Create sample detection result"""
    return DetectionResult(
        boxes=[{
            "x1": 100, "y1": 100, "x2": 200, "y2": 200,
            "confidence": 0.8, "class_name": "sku_present",
            "risk_level": "attention"
        }],
        confidence=0.8,
        image_shape=(640, 640)
    )


class TestDetectionEngine:
    """Test cases for Detection Engine"""
    
    def test_initialization(self, detection_engine):
        """Test detection engine initialization"""
        assert not detection_engine.is_monitoring
        assert detection_engine.monitoring_task is None
        assert len(detection_engine.detection_history) == 0
        assert detection_engine.metrics["total_detections"] == 0
    
    @pytest.mark.asyncio
    async def test_start_monitoring_success(self, detection_engine, mock_image_processor):
        """Test successful monitoring start"""
        mock_image_processor.initialize_camera.return_value = True
        
        success = await detection_engine.start_monitoring()
        
        assert success
        assert detection_engine.is_monitoring
        assert detection_engine.monitoring_task is not None
        
        # Cleanup
        await detection_engine.stop_monitoring()
    
    @pytest.mark.asyncio
    async def test_start_monitoring_camera_failure(self, detection_engine, mock_image_processor):
        """Test monitoring start with camera failure"""
        mock_image_processor.initialize_camera.return_value = False
        
        success = await detection_engine.start_monitoring()
        
        assert not success
        assert not detection_engine.is_monitoring
    
    @pytest.mark.asyncio
    async def test_start_monitoring_already_active(self, detection_engine):
        """Test starting monitoring when already active"""
        detection_engine.is_monitoring = True
        
        success = await detection_engine.start_monitoring()
        
        assert not success
    
    @pytest.mark.asyncio
    async def test_stop_monitoring(self, detection_engine):
        """Test stopping monitoring"""
        # Start monitoring first
        detection_engine.is_monitoring = True
        detection_engine.monitoring_task = Mock()
        detection_engine.monitoring_task.cancel = Mock()
        
        await detection_engine.stop_monitoring()
        
        assert not detection_engine.is_monitoring
        detection_engine.monitoring_task.cancel.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_detect_objects(self, detection_engine, sample_image, sample_detection_result, mock_image_processor, mock_ai_model):
        """Test object detection"""
        mock_image_processor.preprocess_image.return_value = sample_image
        mock_ai_model.detect.return_value = sample_detection_result
        
        result = await detection_engine.detect_objects(sample_image)
        
        assert isinstance(result, DetectionResult)
        assert result.confidence == 0.8
        mock_image_processor.preprocess_image.assert_called_once()
        mock_ai_model.detect.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_detect_objects_failure(self, detection_engine, sample_image, mock_ai_model):
        """Test object detection failure"""
        mock_ai_model.detect.side_effect = Exception("Detection failed")
        
        result = await detection_engine.detect_objects(sample_image)
        
        assert isinstance(result, DetectionResult)
        assert len(result.boxes) == 0
        assert result.confidence == 0.0
    
    @pytest.mark.asyncio
    async def test_detect_from_file_success(self, detection_engine, sample_image, sample_detection_result, mock_image_processor, mock_ai_model):
        """Test detection from file"""
        mock_image_processor.load_image_from_file.return_value = sample_image
        mock_ai_model.analyze_clearing_status.return_value = {
            "status": "not_cleared",
            "risk_level": "attention"
        }
        
        with patch.object(detection_engine, 'detect_objects', return_value=sample_detection_result):
            result = await detection_engine.detect_from_file("test_image.jpg")
        
        assert "detection_result" in result
        assert "analysis" in result
        assert "image" in result
        assert "timestamp" in result
        assert "error" not in result
    
    @pytest.mark.asyncio
    async def test_detect_from_file_load_failure(self, detection_engine, mock_image_processor):
        """Test detection from file with load failure"""
        mock_image_processor.load_image_from_file.return_value = None
        
        result = await detection_engine.detect_from_file("nonexistent.jpg")
        
        assert "error" in result
        assert result["error"] == "Failed to load image"
    
    @pytest.mark.asyncio
    async def test_detect_from_bytes_success(self, detection_engine, sample_image, sample_detection_result, mock_image_processor, mock_ai_model):
        """Test detection from bytes"""
        image_bytes = b"fake_image_data"
        mock_image_processor.load_image_from_bytes.return_value = sample_image
        mock_ai_model.analyze_clearing_status.return_value = {
            "status": "cleared",
            "risk_level": "normal"
        }
        
        with patch.object(detection_engine, 'detect_objects', return_value=sample_detection_result):
            result = await detection_engine.detect_from_bytes(image_bytes)
        
        assert "detection_result" in result
        assert "analysis" in result
        assert "image" in result
        assert "timestamp" in result
        assert "error" not in result
    
    @pytest.mark.asyncio
    async def test_detect_from_bytes_load_failure(self, detection_engine, mock_image_processor):
        """Test detection from bytes with load failure"""
        mock_image_processor.load_image_from_bytes.return_value = None
        
        result = await detection_engine.detect_from_bytes(b"invalid_data")
        
        assert "error" in result
        assert result["error"] == "Failed to load image from bytes"
    
    def test_update_metrics_success(self, detection_engine):
        """Test metrics update on success"""
        detection_engine._update_metrics(1.5, True)
        
        assert detection_engine.metrics["total_detections"] == 1
        assert detection_engine.metrics["successful_detections"] == 1
        assert detection_engine.metrics["failed_detections"] == 0
        assert detection_engine.metrics["average_processing_time"] == 1.5
    
    def test_update_metrics_failure(self, detection_engine):
        """Test metrics update on failure"""
        detection_engine._update_metrics(0.0, False)
        
        assert detection_engine.metrics["total_detections"] == 1
        assert detection_engine.metrics["successful_detections"] == 0
        assert detection_engine.metrics["failed_detections"] == 1
        assert detection_engine.metrics["average_processing_time"] == 0.0
    
    def test_get_status(self, detection_engine, mock_ai_model):
        """Test status retrieval"""
        mock_ai_model.is_loaded = True
        
        status = detection_engine.get_status()
        
        assert "is_monitoring" in status
        assert "camera_active" in status
        assert "model_loaded" in status
        assert "metrics" in status
        assert "recent_detections" in status
    
    def test_get_recent_history_empty(self, detection_engine):
        """Test getting recent history when empty"""
        history = detection_engine.get_recent_history(5)
        
        assert len(history) == 0
    
    def test_get_recent_history_with_data(self, detection_engine):
        """Test getting recent history with data"""
        # Add some fake history
        detection_engine.detection_history = [
            {"timestamp": 1.0, "analysis": {"status": "cleared"}},
            {"timestamp": 2.0, "analysis": {"status": "not_cleared"}},
            {"timestamp": 3.0, "analysis": {"status": "cleared"}}
        ]
        
        history = detection_engine.get_recent_history(2)
        
        assert len(history) == 2
        assert history[0]["timestamp"] == 2.0
        assert history[1]["timestamp"] == 3.0
    
    @pytest.mark.asyncio
    async def test_get_live_frame_success(self, detection_engine, sample_image, sample_detection_result, mock_image_processor):
        """Test getting live frame"""
        mock_image_processor.capture_frame.return_value = sample_image
        
        with patch.object(detection_engine, 'detect_objects', return_value=sample_detection_result):
            frame_data = await detection_engine.get_live_frame()
        
        assert frame_data == "base64_image_data"
        mock_image_processor.draw_detections.assert_called_once()
        mock_image_processor.image_to_base64.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_live_frame_no_camera(self, detection_engine, mock_image_processor):
        """Test getting live frame without camera"""
        mock_image_processor.is_camera_active = False
        
        frame_data = await detection_engine.get_live_frame()
        
        assert frame_data is None
    
    @pytest.mark.asyncio
    async def test_get_live_frame_capture_failure(self, detection_engine, mock_image_processor):
        """Test getting live frame with capture failure"""
        mock_image_processor.capture_frame.return_value = None
        
        frame_data = await detection_engine.get_live_frame()
        
        assert frame_data is None
    
    @pytest.mark.asyncio
    async def test_cleanup(self, detection_engine):
        """Test cleanup"""
        detection_engine.is_monitoring = True
        detection_engine.monitoring_task = Mock()
        detection_engine.monitoring_task.cancel = Mock()
        
        await detection_engine.cleanup()
        
        assert not detection_engine.is_monitoring


if __name__ == "__main__":
    pytest.main([__file__])
