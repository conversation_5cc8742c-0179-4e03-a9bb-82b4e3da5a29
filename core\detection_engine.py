"""
Detection Engine for Conveyor Belt Monitoring
Orchestrates AI model inference, image processing, and result analysis
"""
import asyncio
import time
from typing import Dict, Any, Optional, List
from loguru import logger
import json

from config import settings
from core.ai_model import AIModelManager, DetectionResult
from core.image_processor import ImageProcessor
from core.database import DatabaseManager


class DetectionEngine:
    """Main detection engine that orchestrates the monitoring process"""
    
    def __init__(self, ai_model: AIModelManager, image_processor: ImageProcessor, database: DatabaseManager):
        self.ai_model = ai_model
        self.image_processor = image_processor
        self.database = database
        self.is_monitoring = False
        self.monitoring_task = None
        self.detection_history = []
        self.max_history = 100
        
        # Performance metrics
        self.metrics = {
            "total_detections": 0,
            "successful_detections": 0,
            "failed_detections": 0,
            "average_processing_time": 0.0,
            "last_detection_time": None
        }
    
    async def start_monitoring(self) -> bool:
        """Start continuous monitoring of the conveyor belt"""
        if self.is_monitoring:
            logger.warning("Monitoring already active")
            return False
        
        try:
            # Initialize camera
            camera_init = await self.image_processor.initialize_camera()
            if not camera_init:
                logger.error("Failed to initialize camera for monitoring")
                return False
            
            self.is_monitoring = True
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            logger.info("Conveyor belt monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start monitoring: {str(e)}")
            self.is_monitoring = False
            return False
    
    async def stop_monitoring(self):
        """Stop continuous monitoring"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Conveyor belt monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        logger.info("Starting monitoring loop")
        
        while self.is_monitoring:
            try:
                start_time = time.time()
                
                # Capture frame
                frame = await self.image_processor.capture_frame()
                if frame is None:
                    logger.warning("Failed to capture frame")
                    await asyncio.sleep(1)
                    continue
                
                # Perform detection
                detection_result = await self.detect_objects(frame)
                
                # Analyze results
                analysis = self.ai_model.analyze_clearing_status(detection_result)
                
                # Store results
                await self._store_detection_result(detection_result, analysis, frame)
                
                # Update metrics
                processing_time = time.time() - start_time
                self._update_metrics(processing_time, True)
                
                # Log significant events
                if analysis["risk_level"] in ["high", "critical"]:
                    logger.warning(f"High risk detection: {analysis['status']} - {analysis['issues']}")
                
                # Wait for next detection interval
                await asyncio.sleep(settings.detection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitoring loop error: {str(e)}")
                self._update_metrics(0, False)
                await asyncio.sleep(1)
        
        logger.info("Monitoring loop ended")
    
    async def detect_objects(self, image) -> DetectionResult:
        """Perform object detection on a single image"""
        try:
            # Preprocess image
            processed_image = await self.image_processor.preprocess_image(image)
            
            # Run AI model inference
            detection_result = await self.ai_model.detect(processed_image)
            
            return detection_result
            
        except Exception as e:
            logger.error(f"Object detection failed: {str(e)}")
            # Return empty result
            return DetectionResult(boxes=[], confidence=0.0, image_shape=image.shape[:2])
    
    async def detect_from_file(self, file_path: str) -> Dict[str, Any]:
        """Perform detection on an image file"""
        try:
            # Load image
            image = await self.image_processor.load_image_from_file(file_path)
            if image is None:
                return {"error": "Failed to load image"}
            
            # Perform detection
            detection_result = await self.detect_objects(image)
            
            # Analyze results
            analysis = self.ai_model.analyze_clearing_status(detection_result)
            
            # Draw detections on image
            result_image = self.image_processor.draw_detections(image, detection_result.boxes)
            
            # Convert to base64 for web display
            image_base64 = self.image_processor.image_to_base64(result_image)
            
            return {
                "detection_result": detection_result.to_dict(),
                "analysis": analysis,
                "image": image_base64,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"File detection failed: {str(e)}")
            return {"error": str(e)}
    
    async def detect_from_bytes(self, image_bytes: bytes) -> Dict[str, Any]:
        """Perform detection on image bytes"""
        try:
            # Load image from bytes
            image = await self.image_processor.load_image_from_bytes(image_bytes)
            if image is None:
                return {"error": "Failed to load image from bytes"}
            
            # Perform detection
            detection_result = await self.detect_objects(image)
            
            # Analyze results
            analysis = self.ai_model.analyze_clearing_status(detection_result)
            
            # Draw detections on image
            result_image = self.image_processor.draw_detections(image, detection_result.boxes)
            
            # Convert to base64 for web display
            image_base64 = self.image_processor.image_to_base64(result_image)
            
            # Store results
            await self._store_detection_result(detection_result, analysis, image)
            
            return {
                "detection_result": detection_result.to_dict(),
                "analysis": analysis,
                "image": image_base64,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Bytes detection failed: {str(e)}")
            return {"error": str(e)}
    
    async def _store_detection_result(self, detection_result: DetectionResult, analysis: Dict[str, Any], image):
        """Store detection result in database and history"""
        try:
            # Prepare data for storage
            result_data = {
                "timestamp": detection_result.timestamp,
                "detection_count": len(detection_result.boxes),
                "confidence": detection_result.confidence,
                "status": analysis["status"],
                "risk_level": analysis["risk_level"],
                "clearing_percentage": analysis["clearing_percentage"],
                "issues": json.dumps(analysis["issues"]),
                "recommendations": json.dumps(analysis["recommendations"]),
                "boxes": json.dumps(detection_result.boxes)
            }
            
            # Store in database
            await self.database.store_detection_result(result_data)
            
            # Add to history (keep only recent results)
            self.detection_history.append({
                "timestamp": detection_result.timestamp,
                "analysis": analysis,
                "detection_count": len(detection_result.boxes)
            })
            
            # Trim history if too long
            if len(self.detection_history) > self.max_history:
                self.detection_history = self.detection_history[-self.max_history:]
            
        except Exception as e:
            logger.error(f"Failed to store detection result: {str(e)}")
    
    def _update_metrics(self, processing_time: float, success: bool):
        """Update performance metrics"""
        self.metrics["total_detections"] += 1
        
        if success:
            self.metrics["successful_detections"] += 1
            
            # Update average processing time
            current_avg = self.metrics["average_processing_time"]
            total_successful = self.metrics["successful_detections"]
            self.metrics["average_processing_time"] = (
                (current_avg * (total_successful - 1) + processing_time) / total_successful
            )
        else:
            self.metrics["failed_detections"] += 1
        
        self.metrics["last_detection_time"] = time.time()
    
    def get_status(self) -> Dict[str, Any]:
        """Get current detection engine status"""
        return {
            "is_monitoring": self.is_monitoring,
            "camera_active": self.image_processor.is_camera_active,
            "model_loaded": self.ai_model.is_loaded,
            "metrics": self.metrics.copy(),
            "recent_detections": len(self.detection_history),
            "last_detection": self.detection_history[-1] if self.detection_history else None
        }
    
    def get_recent_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent detection history"""
        return self.detection_history[-limit:] if self.detection_history else []
    
    async def get_live_frame(self) -> Optional[str]:
        """Get current live frame as base64 string"""
        try:
            if not self.image_processor.is_camera_active:
                return None
            
            frame = await self.image_processor.capture_frame()
            if frame is None:
                return None
            
            # Perform quick detection for live view
            detection_result = await self.detect_objects(frame)
            
            # Draw detections
            result_image = self.image_processor.draw_detections(frame, detection_result.boxes)
            
            # Convert to base64
            return self.image_processor.image_to_base64(result_image)
            
        except Exception as e:
            logger.error(f"Live frame capture failed: {str(e)}")
            return None
    
    async def cleanup(self):
        """Cleanup detection engine resources"""
        await self.stop_monitoring()
        logger.info("Detection Engine cleaned up")
