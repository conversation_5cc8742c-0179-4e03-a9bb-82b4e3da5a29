"""
SKU Classification and Analysis Module
Handles SKU identification, clearing status analysis, and mixed code detection
"""
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from loguru import logger
import cv2
from collections import defaultdict, Counter
import time

from config import settings


@dataclass
class SKUInfo:
    """Information about a detected SKU"""
    sku_id: str
    confidence: float
    bbox: Tuple[float, float, float, float]  # x1, y1, x2, y2
    area: float
    center: Tuple[float, float]
    color_profile: Optional[Dict[str, float]] = None
    shape_features: Optional[Dict[str, float]] = None


class SKUClassifier:
    """Advanced SKU classification and analysis"""
    
    def __init__(self):
        # SKU database - in production this would be loaded from a database
        self.sku_database = {
            "SKU001": {"name": "Product A", "color": "red", "shape": "rectangular"},
            "SKU002": {"name": "Product B", "color": "blue", "shape": "circular"},
            "SKU003": {"name": "Product C", "color": "green", "shape": "square"},
            "SKU004": {"name": "Product D", "color": "yellow", "shape": "rectangular"},
            "SKU005": {"name": "Product E", "color": "purple", "shape": "circular"}
        }
        
        # Color ranges for SKU identification (HSV)
        self.color_ranges = {
            "red": [(0, 50, 50), (10, 255, 255), (170, 50, 50), (180, 255, 255)],
            "blue": [(100, 50, 50), (130, 255, 255)],
            "green": [(40, 50, 50), (80, 255, 255)],
            "yellow": [(20, 50, 50), (40, 255, 255)],
            "purple": [(130, 50, 50), (170, 255, 255)]
        }
        
        # Clearing thresholds
        self.clearing_thresholds = {
            "empty_threshold": 0.05,  # Less than 5% coverage = empty
            "cleared_threshold": 0.15,  # Less than 15% coverage = cleared
            "mixed_threshold": 0.3,  # More than 30% different SKUs = mixed
            "contamination_threshold": 0.1  # More than 10% unknown objects = contaminated
        }
    
    def analyze_sku_distribution(self, detection_boxes: List[Dict]) -> Dict[str, Any]:
        """Analyze SKU distribution and detect mixed codes"""
        analysis = {
            "total_objects": len(detection_boxes),
            "sku_counts": defaultdict(int),
            "coverage_percentage": 0.0,
            "dominant_sku": None,
            "mixed_sku_detected": False,
            "contamination_detected": False,
            "clearing_status": "unknown",
            "risk_assessment": "normal"
        }
        
        if not detection_boxes:
            analysis.update({
                "clearing_status": "empty",
                "risk_assessment": "normal"
            })
            return analysis
        
        # Calculate total belt area (assuming standard belt dimensions)
        belt_area = settings.image_width * settings.image_height
        total_object_area = 0
        
        # Analyze each detected object
        sku_areas = defaultdict(float)
        unknown_area = 0
        
        for box in detection_boxes:
            class_name = box.get("class_name", "unknown")
            area = box.get("width", 0) * box.get("height", 0)
            total_object_area += area
            
            if class_name in ["sku_present", "mixed_sku"]:
                # Try to identify specific SKU
                sku_id = self._identify_sku_from_box(box)
                if sku_id:
                    analysis["sku_counts"][sku_id] += 1
                    sku_areas[sku_id] += area
                else:
                    analysis["sku_counts"]["unknown_sku"] += 1
                    unknown_area += area
            elif class_name == "foreign_object":
                analysis["sku_counts"]["foreign_object"] += 1
                unknown_area += area
        
        # Calculate coverage percentage
        analysis["coverage_percentage"] = (total_object_area / belt_area) * 100
        
        # Determine dominant SKU
        if analysis["sku_counts"]:
            analysis["dominant_sku"] = max(analysis["sku_counts"], key=analysis["sku_counts"].get)
        
        # Check for mixed SKU scenario
        sku_types = [k for k in analysis["sku_counts"].keys() if k not in ["unknown_sku", "foreign_object"]]
        if len(sku_types) > 1:
            # Calculate diversity ratio
            total_sku_area = sum(sku_areas.values())
            if total_sku_area > 0:
                diversity_ratio = 1 - (max(sku_areas.values()) / total_sku_area)
                if diversity_ratio > self.clearing_thresholds["mixed_threshold"]:
                    analysis["mixed_sku_detected"] = True
        
        # Check for contamination
        contamination_ratio = unknown_area / total_object_area if total_object_area > 0 else 0
        if contamination_ratio > self.clearing_thresholds["contamination_threshold"]:
            analysis["contamination_detected"] = True
        
        # Determine clearing status
        analysis["clearing_status"] = self._determine_clearing_status(analysis)
        analysis["risk_assessment"] = self._assess_risk_level(analysis)
        
        return analysis
    
    def _identify_sku_from_box(self, box: Dict) -> Optional[str]:
        """Identify specific SKU from detection box (placeholder implementation)"""
        # In a real implementation, this would use additional features like:
        # - Color analysis
        # - Shape analysis
        # - Barcode/QR code reading
        # - Template matching
        
        # For now, return a mock SKU ID based on confidence
        confidence = box.get("confidence", 0)
        if confidence > 0.8:
            return "SKU001"
        elif confidence > 0.6:
            return "SKU002"
        elif confidence > 0.4:
            return "SKU003"
        else:
            return None
    
    def _determine_clearing_status(self, analysis: Dict) -> str:
        """Determine the clearing status based on analysis"""
        coverage = analysis["coverage_percentage"]
        
        if analysis["contamination_detected"]:
            return "contaminated"
        elif analysis["mixed_sku_detected"]:
            return "mixed_sku"
        elif coverage < self.clearing_thresholds["empty_threshold"]:
            return "empty"
        elif coverage < self.clearing_thresholds["cleared_threshold"]:
            return "cleared"
        else:
            return "not_cleared"
    
    def _assess_risk_level(self, analysis: Dict) -> str:
        """Assess risk level based on analysis"""
        if analysis["contamination_detected"]:
            return "critical"
        elif analysis["mixed_sku_detected"]:
            return "high"
        elif analysis["clearing_status"] == "not_cleared":
            return "attention"
        else:
            return "normal"
    
    def extract_color_features(self, image: np.ndarray, bbox: Tuple[float, float, float, float]) -> Dict[str, float]:
        """Extract color features from a bounding box region"""
        try:
            x1, y1, x2, y2 = map(int, bbox)
            
            # Extract region of interest
            roi = image[y1:y2, x1:x2]
            
            if roi.size == 0:
                return {}
            
            # Convert to HSV for better color analysis
            hsv_roi = cv2.cvtColor(roi, cv2.COLOR_RGB2HSV)
            
            # Calculate color histogram
            hist_h = cv2.calcHist([hsv_roi], [0], None, [180], [0, 180])
            hist_s = cv2.calcHist([hsv_roi], [1], None, [256], [0, 256])
            hist_v = cv2.calcHist([hsv_roi], [2], None, [256], [0, 256])
            
            # Normalize histograms
            hist_h = hist_h.flatten() / hist_h.sum()
            hist_s = hist_s.flatten() / hist_s.sum()
            hist_v = hist_v.flatten() / hist_v.sum()
            
            # Calculate dominant color
            dominant_hue = np.argmax(hist_h)
            dominant_saturation = np.argmax(hist_s)
            dominant_value = np.argmax(hist_v)
            
            # Calculate color statistics
            mean_color = np.mean(hsv_roi, axis=(0, 1))
            std_color = np.std(hsv_roi, axis=(0, 1))
            
            return {
                "dominant_hue": float(dominant_hue),
                "dominant_saturation": float(dominant_saturation),
                "dominant_value": float(dominant_value),
                "mean_hue": float(mean_color[0]),
                "mean_saturation": float(mean_color[1]),
                "mean_value": float(mean_color[2]),
                "std_hue": float(std_color[0]),
                "std_saturation": float(std_color[1]),
                "std_value": float(std_color[2])
            }
            
        except Exception as e:
            logger.error(f"Color feature extraction failed: {str(e)}")
            return {}
    
    def extract_shape_features(self, image: np.ndarray, bbox: Tuple[float, float, float, float]) -> Dict[str, float]:
        """Extract shape features from a bounding box region"""
        try:
            x1, y1, x2, y2 = map(int, bbox)
            
            # Extract region of interest
            roi = image[y1:y2, x1:x2]
            
            if roi.size == 0:
                return {}
            
            # Convert to grayscale
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_RGB2GRAY)
            
            # Apply threshold to get binary image
            _, binary = cv2.threshold(gray_roi, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Find contours
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return {}
            
            # Get largest contour
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Calculate shape features
            area = cv2.contourArea(largest_contour)
            perimeter = cv2.arcLength(largest_contour, True)
            
            # Aspect ratio
            x, y, w, h = cv2.boundingRect(largest_contour)
            aspect_ratio = float(w) / h if h > 0 else 0
            
            # Extent (ratio of contour area to bounding rectangle area)
            rect_area = w * h
            extent = float(area) / rect_area if rect_area > 0 else 0
            
            # Solidity (ratio of contour area to convex hull area)
            hull = cv2.convexHull(largest_contour)
            hull_area = cv2.contourArea(hull)
            solidity = float(area) / hull_area if hull_area > 0 else 0
            
            # Circularity
            circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0
            
            return {
                "area": float(area),
                "perimeter": float(perimeter),
                "aspect_ratio": aspect_ratio,
                "extent": extent,
                "solidity": solidity,
                "circularity": circularity
            }
            
        except Exception as e:
            logger.error(f"Shape feature extraction failed: {str(e)}")
            return {}
    
    def generate_clearing_report(self, analysis: Dict, detection_history: List[Dict]) -> Dict[str, Any]:
        """Generate comprehensive clearing report"""
        report = {
            "timestamp": time.time(),
            "current_status": analysis,
            "trend_analysis": self._analyze_trends(detection_history),
            "recommendations": self._generate_recommendations(analysis),
            "quality_score": self._calculate_quality_score(analysis),
            "compliance_status": self._check_compliance(analysis)
        }
        
        return report
    
    def _analyze_trends(self, history: List[Dict]) -> Dict[str, Any]:
        """Analyze trends from detection history"""
        if len(history) < 2:
            return {"trend": "insufficient_data"}
        
        # Analyze clearing efficiency over time
        recent_clearings = [h["analysis"]["clearing_percentage"] for h in history[-10:]]
        
        if len(recent_clearings) >= 2:
            trend = "improving" if recent_clearings[-1] > recent_clearings[0] else "declining"
        else:
            trend = "stable"
        
        return {
            "trend": trend,
            "average_clearing": np.mean(recent_clearings) if recent_clearings else 0,
            "clearing_variance": np.var(recent_clearings) if recent_clearings else 0,
            "samples_analyzed": len(recent_clearings)
        }
    
    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        status = analysis["clearing_status"]
        risk = analysis["risk_assessment"]
        
        if status == "contaminated":
            recommendations.extend([
                "IMMEDIATE ACTION: Stop production and inspect belt",
                "Remove foreign objects before resuming",
                "Check upstream processes for contamination source",
                "Implement additional quality control measures"
            ])
        elif status == "mixed_sku":
            recommendations.extend([
                "Review SKU separation procedures",
                "Check sorting mechanisms for proper operation",
                "Verify batch changeover protocols",
                "Consider additional training for operators"
            ])
        elif status == "not_cleared":
            recommendations.extend([
                "Complete belt clearing before next batch",
                "Check clearing mechanism efficiency",
                "Verify clearing time allowances",
                "Monitor for recurring clearing issues"
            ])
        elif status in ["cleared", "empty"]:
            recommendations.append("Belt status normal - ready for production")
        
        return recommendations
    
    def _calculate_quality_score(self, analysis: Dict) -> float:
        """Calculate overall quality score (0-100)"""
        base_score = 100.0
        
        # Deduct points based on issues
        if analysis["contamination_detected"]:
            base_score -= 50
        if analysis["mixed_sku_detected"]:
            base_score -= 30
        if analysis["clearing_status"] == "not_cleared":
            base_score -= 20
        
        # Adjust based on coverage
        coverage = analysis["coverage_percentage"]
        if coverage > 20:  # High coverage indicates poor clearing
            base_score -= min(30, coverage - 20)
        
        return max(0.0, base_score)
    
    def _check_compliance(self, analysis: Dict) -> Dict[str, Any]:
        """Check compliance with quality standards"""
        compliance = {
            "overall_compliant": True,
            "violations": [],
            "warnings": []
        }
        
        if analysis["contamination_detected"]:
            compliance["overall_compliant"] = False
            compliance["violations"].append("Foreign object contamination detected")
        
        if analysis["mixed_sku_detected"]:
            compliance["overall_compliant"] = False
            compliance["violations"].append("Mixed SKU scenario detected")
        
        if analysis["coverage_percentage"] > 15:
            compliance["warnings"].append("High residual coverage on belt")
        
        return compliance
