# KR Conveyor Belt Target Identification System Configuration

# Application Settings
DEBUG=false
HOST=0.0.0.0
PORT=8000
RELOAD=false

# AI Model Settings
MODEL_PATH=models/yolov8n.pt
MODEL_CONFIDENCE=0.5
MODEL_IOU_THRESHOLD=0.45
DEVICE=cpu

# Image Processing Settings
IMAGE_WIDTH=640
IMAGE_HEIGHT=640
MAX_IMAGE_SIZE=10485760

# Camera Settings
CAMERA_INDEX=0
CAMERA_FPS=30

# Detection Settings
ALERT_THRESHOLD=0.8
DETECTION_INTERVAL=1

# Database Settings
DATABASE_URL=sqlite:///./conveyor_monitoring.db
REDIS_URL=redis://localhost:6379

# Logging Settings
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# Email Alert Settings
EMAIL_ENABLED=false
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
