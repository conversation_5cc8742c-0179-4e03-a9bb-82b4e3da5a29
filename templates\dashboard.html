<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KR Conveyor Belt Monitor - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            opacity: 0.9;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            border: 1px solid #e1e8ed;
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running { background: #27ae60; }
        .status-stopped { background: #e74c3c; }
        .status-warning { background: #f39c12; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid;
        }
        
        .alert-critical {
            background: #fdf2f2;
            border-color: #e74c3c;
            color: #c0392b;
        }
        
        .alert-high {
            background: #fef9e7;
            border-color: #f39c12;
            color: #d68910;
        }
        
        .alert-attention {
            background: #eaf4fd;
            border-color: #3498db;
            color: #2980b9;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .live-feed {
            text-align: center;
        }
        
        .live-feed img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .detection-history {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .history-item {
            padding: 0.75rem;
            border-bottom: 1px solid #ecf0f1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .timestamp {
            font-size: 0.8rem;
            color: #7f8c8d;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }
        
        .error {
            background: #fdf2f2;
            color: #e74c3c;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>KR Conveyor Belt Target Identification System</h1>
        <p>Real-time monitoring and quality control dashboard</p>
    </div>
    
    <div class="container">
        <div class="dashboard-grid">
            <!-- System Status Card -->
            <div class="card">
                <h3>System Status</h3>
                <div id="system-status">
                    <div class="loading">Loading system status...</div>
                </div>
                <div class="controls">
                    <button class="btn btn-success" onclick="startMonitoring()">Start</button>
                    <button class="btn btn-danger" onclick="stopMonitoring()">Stop</button>
                    <button class="btn" onclick="refreshStatus()">Refresh</button>
                </div>
            </div>
            
            <!-- Live Feed Card -->
            <div class="card">
                <h3>Live Camera Feed</h3>
                <div class="live-feed">
                    <img id="live-image" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIGNhbWVyYSBmZWVkPC90ZXh0Pjwvc3ZnPg==" alt="Live camera feed">
                </div>
                <div class="controls">
                    <button class="btn" onclick="refreshLiveFeed()">Refresh Feed</button>
                    <button class="btn" onclick="captureFrame()">Capture</button>
                </div>
            </div>
            
            <!-- Active Alerts Card -->
            <div class="card">
                <h3>Active Alerts</h3>
                <div id="active-alerts">
                    <div class="loading">Loading alerts...</div>
                </div>
            </div>
            
            <!-- Detection Metrics Card -->
            <div class="card">
                <h3>Detection Metrics</h3>
                <div id="detection-metrics">
                    <div class="loading">Loading metrics...</div>
                </div>
            </div>
            
            <!-- Recent Detections Card -->
            <div class="card">
                <h3>Recent Detections</h3>
                <div class="detection-history" id="detection-history">
                    <div class="loading">Loading detection history...</div>
                </div>
            </div>
            
            <!-- Upload Detection Card -->
            <div class="card">
                <h3>Upload Image for Detection</h3>
                <input type="file" id="image-upload" accept="image/*" style="margin-bottom: 1rem;">
                <button class="btn" onclick="uploadImage()">Analyze Image</button>
                <div id="upload-result" style="margin-top: 1rem;"></div>
            </div>
        </div>
    </div>
    
    <script>
        // WebSocket connection for real-time updates
        let ws = null;
        let reconnectInterval = null;
        
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                console.log('WebSocket connected');
                if (reconnectInterval) {
                    clearInterval(reconnectInterval);
                    reconnectInterval = null;
                }
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            ws.onclose = function() {
                console.log('WebSocket disconnected');
                // Attempt to reconnect every 5 seconds
                if (!reconnectInterval) {
                    reconnectInterval = setInterval(connectWebSocket, 5000);
                }
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }
        
        function handleWebSocketMessage(data) {
            if (data.type === 'alert') {
                // Handle new alert
                loadActiveAlerts();
            } else if (data.type === 'status') {
                // Handle status update
                updateSystemStatus(data);
            }
        }
        
        // API functions
        async function apiCall(endpoint, options = {}) {
            try {
                const response = await fetch(endpoint, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        }
        
        // System control functions
        async function startMonitoring() {
            try {
                await apiCall('/api/detection/start', { method: 'POST' });
                showNotification('Monitoring started successfully', 'success');
                refreshStatus();
            } catch (error) {
                showNotification('Failed to start monitoring: ' + error.message, 'error');
            }
        }
        
        async function stopMonitoring() {
            try {
                await apiCall('/api/detection/stop', { method: 'POST' });
                showNotification('Monitoring stopped successfully', 'success');
                refreshStatus();
            } catch (error) {
                showNotification('Failed to stop monitoring: ' + error.message, 'error');
            }
        }
        
        // Data loading functions
        async function loadSystemStatus() {
            try {
                const data = await apiCall('/api/detection/status');
                updateSystemStatus(data);
            } catch (error) {
                document.getElementById('system-status').innerHTML = 
                    `<div class="error">Failed to load system status: ${error.message}</div>`;
            }
        }
        
        function updateSystemStatus(data) {
            const statusHtml = `
                <div class="metric">
                    <span>Status:</span>
                    <span class="metric-value">
                        <span class="status-indicator ${data.status === 'running' ? 'status-running' : 'status-stopped'}"></span>
                        ${data.status}
                    </span>
                </div>
                <div class="metric">
                    <span>Camera:</span>
                    <span class="metric-value">${data.detection_engine?.camera_active ? 'Active' : 'Inactive'}</span>
                </div>
                <div class="metric">
                    <span>Model:</span>
                    <span class="metric-value">${data.detection_engine?.model_loaded ? 'Loaded' : 'Not Loaded'}</span>
                </div>
                <div class="metric">
                    <span>Monitoring:</span>
                    <span class="metric-value">${data.detection_engine?.is_monitoring ? 'Active' : 'Stopped'}</span>
                </div>
            `;
            document.getElementById('system-status').innerHTML = statusHtml;
        }
        
        async function loadActiveAlerts() {
            try {
                const data = await apiCall('/api/dashboard/alerts?active_only=true');
                const alertsHtml = data.alerts.length > 0 ? 
                    data.alerts.map(alert => `
                        <div class="alert alert-${alert.alert_type}">
                            <strong>${alert.title}</strong><br>
                            ${alert.message}<br>
                            <small class="timestamp">${new Date(alert.timestamp * 1000).toLocaleString()}</small>
                            <div style="margin-top: 0.5rem;">
                                <button class="btn" onclick="acknowledgeAlert('${alert.id}')">Acknowledge</button>
                                <button class="btn" onclick="resolveAlert('${alert.id}')">Resolve</button>
                            </div>
                        </div>
                    `).join('') :
                    '<div style="text-align: center; color: #27ae60;">No active alerts</div>';
                
                document.getElementById('active-alerts').innerHTML = alertsHtml;
            } catch (error) {
                document.getElementById('active-alerts').innerHTML = 
                    `<div class="error">Failed to load alerts: ${error.message}</div>`;
            }
        }
        
        async function loadDetectionMetrics() {
            try {
                const data = await apiCall('/api/detection/metrics');
                const metricsHtml = `
                    <div class="metric">
                        <span>Total Detections:</span>
                        <span class="metric-value">${data.total_detections || 0}</span>
                    </div>
                    <div class="metric">
                        <span>Successful:</span>
                        <span class="metric-value">${data.successful_detections || 0}</span>
                    </div>
                    <div class="metric">
                        <span>Failed:</span>
                        <span class="metric-value">${data.failed_detections || 0}</span>
                    </div>
                    <div class="metric">
                        <span>Avg Processing Time:</span>
                        <span class="metric-value">${(data.average_processing_time || 0).toFixed(2)}s</span>
                    </div>
                `;
                document.getElementById('detection-metrics').innerHTML = metricsHtml;
            } catch (error) {
                document.getElementById('detection-metrics').innerHTML = 
                    `<div class="error">Failed to load metrics: ${error.message}</div>`;
            }
        }
        
        async function loadDetectionHistory() {
            try {
                const data = await apiCall('/api/detection/history?limit=10');
                const historyHtml = data.history.length > 0 ?
                    data.history.map(item => `
                        <div class="history-item">
                            <div>
                                <strong>${item.analysis?.status || 'Unknown'}</strong><br>
                                <span class="timestamp">${new Date(item.timestamp * 1000).toLocaleString()}</span>
                            </div>
                            <div class="metric-value">${item.detection_count} objects</div>
                        </div>
                    `).join('') :
                    '<div style="text-align: center; color: #7f8c8d;">No detection history</div>';
                
                document.getElementById('detection-history').innerHTML = historyHtml;
            } catch (error) {
                document.getElementById('detection-history').innerHTML = 
                    `<div class="error">Failed to load history: ${error.message}</div>`;
            }
        }
        
        async function refreshLiveFeed() {
            try {
                const data = await apiCall('/api/detection/live-frame');
                document.getElementById('live-image').src = data.image;
            } catch (error) {
                showNotification('Failed to refresh live feed: ' + error.message, 'error');
            }
        }
        
        // Alert management functions
        async function acknowledgeAlert(alertId) {
            try {
                await apiCall(`/api/dashboard/alerts/${alertId}/acknowledge`, { method: 'POST' });
                showNotification('Alert acknowledged', 'success');
                loadActiveAlerts();
            } catch (error) {
                showNotification('Failed to acknowledge alert: ' + error.message, 'error');
            }
        }
        
        async function resolveAlert(alertId) {
            try {
                await apiCall(`/api/dashboard/alerts/${alertId}/resolve`, { method: 'POST' });
                showNotification('Alert resolved', 'success');
                loadActiveAlerts();
            } catch (error) {
                showNotification('Failed to resolve alert: ' + error.message, 'error');
            }
        }
        
        // Image upload function
        async function uploadImage() {
            const fileInput = document.getElementById('image-upload');
            const file = fileInput.files[0];
            
            if (!file) {
                showNotification('Please select an image file', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch('/api/detection/detect', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.error) {
                    throw new Error(data.error);
                }
                
                const resultHtml = `
                    <div style="margin-top: 1rem;">
                        <img src="${data.image}" style="max-width: 100%; border-radius: 8px;">
                        <div style="margin-top: 1rem;">
                            <strong>Status:</strong> ${data.analysis.status}<br>
                            <strong>Risk Level:</strong> ${data.analysis.risk_level}<br>
                            <strong>Objects Detected:</strong> ${data.detection_result.detection_count}<br>
                            <strong>Confidence:</strong> ${(data.detection_result.confidence * 100).toFixed(1)}%
                        </div>
                    </div>
                `;
                
                document.getElementById('upload-result').innerHTML = resultHtml;
                showNotification('Image analyzed successfully', 'success');
                
            } catch (error) {
                showNotification('Failed to analyze image: ' + error.message, 'error');
            }
        }
        
        // Utility functions
        function refreshStatus() {
            loadSystemStatus();
            loadDetectionMetrics();
        }
        
        function refreshAll() {
            loadSystemStatus();
            loadActiveAlerts();
            loadDetectionMetrics();
            loadDetectionHistory();
        }
        
        function showNotification(message, type) {
            // Simple notification system
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 1rem;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 1000;
                background: ${type === 'success' ? '#27ae60' : '#e74c3c'};
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
            refreshAll();
            
            // Auto-refresh every 30 seconds
            setInterval(refreshAll, 30000);
            
            // Auto-refresh live feed every 5 seconds
            setInterval(refreshLiveFeed, 5000);
        });
    </script>
</body>
</html>
