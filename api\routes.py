"""
API Routes for the KR Conveyor Belt Target Identification System
"""
from fastapi import APIRouter, HTTPException, UploadFile, File, Depends, Request
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
import asyncio
from loguru import logger
import time

# Import will be handled by main.py through dependency injection
detection_router = APIRouter()
dashboard_router = APIRouter()


# Dependency to get monitor instance (will be injected by main.py)
async def get_monitor():
    from main import monitor
    return monitor


@detection_router.get("/status")
async def get_detection_status(monitor = Depends(get_monitor)):
    """Get current detection system status"""
    try:
        if not monitor.detection_engine:
            raise HTTPException(status_code=503, detail="Detection engine not initialized")
        
        status = monitor.detection_engine.get_status()
        model_info = monitor.ai_model.get_model_info() if monitor.ai_model else {}
        
        return {
            "status": "running" if status["is_monitoring"] else "stopped",
            "detection_engine": status,
            "model_info": model_info,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Status check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@detection_router.post("/start")
async def start_monitoring(monitor = Depends(get_monitor)):
    """Start continuous monitoring"""
    try:
        if not monitor.detection_engine:
            raise HTTPException(status_code=503, detail="Detection engine not initialized")
        
        success = await monitor.detection_engine.start_monitoring()
        
        if success:
            return {"message": "Monitoring started successfully", "status": "running"}
        else:
            raise HTTPException(status_code=500, detail="Failed to start monitoring")
            
    except Exception as e:
        logger.error(f"Start monitoring failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@detection_router.post("/stop")
async def stop_monitoring(monitor = Depends(get_monitor)):
    """Stop continuous monitoring"""
    try:
        if not monitor.detection_engine:
            raise HTTPException(status_code=503, detail="Detection engine not initialized")
        
        await monitor.detection_engine.stop_monitoring()
        return {"message": "Monitoring stopped successfully", "status": "stopped"}
        
    except Exception as e:
        logger.error(f"Stop monitoring failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@detection_router.post("/detect")
async def detect_from_upload(file: UploadFile = File(...), monitor = Depends(get_monitor)):
    """Perform detection on uploaded image"""
    try:
        if not monitor.detection_engine:
            raise HTTPException(status_code=503, detail="Detection engine not initialized")
        
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Read file content
        file_content = await file.read()
        
        # Perform detection
        result = await monitor.detection_engine.detect_from_bytes(file_content)
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Detection from upload failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@detection_router.get("/live-frame")
async def get_live_frame(monitor = Depends(get_monitor)):
    """Get current live frame with detections"""
    try:
        if not monitor.detection_engine:
            raise HTTPException(status_code=503, detail="Detection engine not initialized")
        
        frame_data = await monitor.detection_engine.get_live_frame()
        
        if frame_data is None:
            raise HTTPException(status_code=503, detail="Camera not available or no frame captured")
        
        return {"image": frame_data, "timestamp": time.time()}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Live frame capture failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@detection_router.get("/history")
async def get_detection_history(limit: int = 10, monitor = Depends(get_monitor)):
    """Get recent detection history"""
    try:
        if not monitor.detection_engine:
            raise HTTPException(status_code=503, detail="Detection engine not initialized")
        
        history = monitor.detection_engine.get_recent_history(limit)
        return {"history": history, "count": len(history)}
        
    except Exception as e:
        logger.error(f"Get detection history failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@detection_router.get("/metrics")
async def get_detection_metrics(monitor = Depends(get_monitor)):
    """Get detection performance metrics"""
    try:
        if not monitor.detection_engine:
            raise HTTPException(status_code=503, detail="Detection engine not initialized")
        
        status = monitor.detection_engine.get_status()
        return status.get("metrics", {})
        
    except Exception as e:
        logger.error(f"Get detection metrics failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Dashboard API Routes
@dashboard_router.get("/overview")
async def get_dashboard_overview(monitor = Depends(get_monitor)):
    """Get dashboard overview data"""
    try:
        # Get system status
        detection_status = monitor.detection_engine.get_status() if monitor.detection_engine else {}
        
        # Get recent alerts
        active_alerts = monitor.alert_system.get_active_alerts() if monitor.alert_system else []
        
        # Get recent detection history
        recent_history = monitor.detection_engine.get_recent_history(5) if monitor.detection_engine else []
        
        # Get alert statistics
        alert_stats = monitor.alert_system.get_alert_statistics() if monitor.alert_system else {}
        
        # Get database statistics
        db_stats = await monitor.database.get_statistics(7) if monitor.database else {}
        
        return {
            "system_status": detection_status,
            "active_alerts": active_alerts,
            "recent_detections": recent_history,
            "alert_statistics": alert_stats,
            "database_statistics": db_stats,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Dashboard overview failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@dashboard_router.get("/alerts")
async def get_alerts(active_only: bool = False, limit: int = 50, monitor = Depends(get_monitor)):
    """Get alerts with optional filtering"""
    try:
        if active_only:
            alerts = monitor.alert_system.get_active_alerts() if monitor.alert_system else []
        else:
            alerts = monitor.alert_system.get_alert_history(limit) if monitor.alert_system else []
        
        return {"alerts": alerts, "count": len(alerts)}
        
    except Exception as e:
        logger.error(f"Get alerts failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@dashboard_router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: str, request: Request, monitor = Depends(get_monitor)):
    """Acknowledge an alert"""
    try:
        if not monitor.alert_system:
            raise HTTPException(status_code=503, detail="Alert system not initialized")
        
        # Get user info from request (in production, this would come from authentication)
        user = request.headers.get("X-User-ID", "anonymous")
        
        success = await monitor.alert_system.acknowledge_alert(alert_id, user)
        
        if success:
            # Log audit event
            if monitor.database:
                await monitor.database.log_audit_event(
                    user_id=user,
                    action="acknowledge_alert",
                    resource=f"alert:{alert_id}",
                    ip_address=request.client.host if request.client else None
                )
            
            return {"message": "Alert acknowledged successfully"}
        else:
            raise HTTPException(status_code=404, detail="Alert not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Acknowledge alert failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@dashboard_router.post("/alerts/{alert_id}/resolve")
async def resolve_alert(alert_id: str, request: Request, monitor = Depends(get_monitor)):
    """Resolve an alert"""
    try:
        if not monitor.alert_system:
            raise HTTPException(status_code=503, detail="Alert system not initialized")
        
        # Get user info from request
        user = request.headers.get("X-User-ID", "anonymous")
        
        success = await monitor.alert_system.resolve_alert(alert_id, user)
        
        if success:
            # Log audit event
            if monitor.database:
                await monitor.database.log_audit_event(
                    user_id=user,
                    action="resolve_alert",
                    resource=f"alert:{alert_id}",
                    ip_address=request.client.host if request.client else None
                )
            
            return {"message": "Alert resolved successfully"}
        else:
            raise HTTPException(status_code=404, detail="Alert not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Resolve alert failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@dashboard_router.get("/statistics")
async def get_system_statistics(days: int = 7, monitor = Depends(get_monitor)):
    """Get system statistics for specified period"""
    try:
        if not monitor.database:
            raise HTTPException(status_code=503, detail="Database not initialized")
        
        stats = await monitor.database.get_statistics(days)
        return stats
        
    except Exception as e:
        logger.error(f"Get statistics failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@dashboard_router.get("/detection-results")
async def get_detection_results(
    limit: int = 100, 
    offset: int = 0,
    start_time: Optional[float] = None,
    end_time: Optional[float] = None,
    monitor = Depends(get_monitor)
):
    """Get detection results with optional filtering"""
    try:
        if not monitor.database:
            raise HTTPException(status_code=503, detail="Database not initialized")
        
        results = await monitor.database.get_detection_results(
            limit=limit,
            offset=offset,
            start_time=start_time,
            end_time=end_time
        )
        
        return {"results": results, "count": len(results)}
        
    except Exception as e:
        logger.error(f"Get detection results failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@dashboard_router.get("/system-health")
async def get_system_health(monitor = Depends(get_monitor)):
    """Get comprehensive system health status"""
    try:
        health = {
            "overall_status": "healthy",
            "components": {},
            "timestamp": time.time()
        }
        
        # Check AI model
        if monitor.ai_model:
            model_info = monitor.ai_model.get_model_info()
            health["components"]["ai_model"] = {
                "status": "healthy" if model_info.get("loaded") else "unhealthy",
                "details": model_info
            }
        else:
            health["components"]["ai_model"] = {"status": "unhealthy", "details": "Not initialized"}
        
        # Check detection engine
        if monitor.detection_engine:
            engine_status = monitor.detection_engine.get_status()
            health["components"]["detection_engine"] = {
                "status": "healthy" if engine_status.get("model_loaded") else "unhealthy",
                "details": engine_status
            }
        else:
            health["components"]["detection_engine"] = {"status": "unhealthy", "details": "Not initialized"}
        
        # Check database
        if monitor.database and monitor.database.is_initialized:
            health["components"]["database"] = {"status": "healthy", "details": "Connected"}
        else:
            health["components"]["database"] = {"status": "unhealthy", "details": "Not connected"}
        
        # Check alert system
        if monitor.alert_system:
            alert_stats = monitor.alert_system.get_alert_statistics()
            health["components"]["alert_system"] = {
                "status": "healthy",
                "details": alert_stats
            }
        else:
            health["components"]["alert_system"] = {"status": "unhealthy", "details": "Not initialized"}
        
        # Determine overall status
        component_statuses = [comp["status"] for comp in health["components"].values()]
        if "unhealthy" in component_statuses:
            health["overall_status"] = "degraded" if "healthy" in component_statuses else "unhealthy"
        
        return health
        
    except Exception as e:
        logger.error(f"System health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
