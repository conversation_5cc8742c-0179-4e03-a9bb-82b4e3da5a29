#!/usr/bin/env python3
"""
Setup script for KR Conveyor Belt Target Identification System
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, check=True):
    """Run a shell command"""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"Error running command: {command}")
        print(f"Error output: {result.stderr}")
        sys.exit(1)
    
    return result


def create_directories():
    """Create necessary directories"""
    directories = [
        "data",
        "logs", 
        "models",
        "static",
        "templates",
        "uploads"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"Created directory: {directory}")


def setup_environment():
    """Setup Python environment"""
    print("Setting up Python environment...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    
    # Install requirements
    run_command("pip install -r requirements.txt")
    
    # Copy environment file
    if not Path(".env").exists():
        if Path(".env.example").exists():
            shutil.copy(".env.example", ".env")
            print("Created .env file from .env.example")
        else:
            print("Warning: .env.example not found")


def download_model():
    """Download default YOLO model if not present"""
    model_path = Path("models/yolov8n.pt")
    
    if not model_path.exists():
        print("Downloading default YOLOv8 model...")
        try:
            import ultralytics
            from ultralytics import YOLO
            
            # This will automatically download the model
            model = YOLO("yolov8n.pt")
            
            # Move to models directory
            model_path.parent.mkdir(exist_ok=True)
            if Path("yolov8n.pt").exists():
                shutil.move("yolov8n.pt", model_path)
                print(f"Model saved to: {model_path}")
            
        except ImportError:
            print("Warning: ultralytics not installed, model download skipped")
        except Exception as e:
            print(f"Warning: Model download failed: {e}")


def setup_database():
    """Initialize database"""
    print("Initializing database...")
    
    try:
        # Import and initialize database
        from core.database import DatabaseManager
        import asyncio
        
        async def init_db():
            db = DatabaseManager()
            await db.initialize()
            await db.close()
            print("Database initialized successfully")
        
        asyncio.run(init_db())
        
    except Exception as e:
        print(f"Warning: Database initialization failed: {e}")


def check_system_dependencies():
    """Check system dependencies"""
    print("Checking system dependencies...")
    
    # Check for camera
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("✓ Camera detected")
            cap.release()
        else:
            print("⚠ No camera detected (optional for file-based detection)")
    except ImportError:
        print("⚠ OpenCV not available")
    
    # Check for CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ CUDA available: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠ CUDA not available (will use CPU)")
    except ImportError:
        print("⚠ PyTorch not available")


def create_systemd_service():
    """Create systemd service file for Linux"""
    if os.name != 'posix':
        return
    
    service_content = f"""[Unit]
Description=KR Conveyor Belt Target Identification System
After=network.target

[Service]
Type=simple
User={os.getenv('USER', 'root')}
WorkingDirectory={os.getcwd()}
Environment=PATH={os.environ.get('PATH')}
ExecStart={sys.executable} main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
    
    service_file = Path("/etc/systemd/system/conveyor-monitor.service")
    
    try:
        with open(service_file, 'w') as f:
            f.write(service_content)
        
        run_command("sudo systemctl daemon-reload")
        print(f"Systemd service created: {service_file}")
        print("To enable: sudo systemctl enable conveyor-monitor")
        print("To start: sudo systemctl start conveyor-monitor")
        
    except PermissionError:
        print("Warning: Could not create systemd service (requires sudo)")
    except Exception as e:
        print(f"Warning: Systemd service creation failed: {e}")


def main():
    """Main setup function"""
    print("KR Conveyor Belt Target Identification System Setup")
    print("=" * 50)
    
    # Create directories
    create_directories()
    
    # Setup Python environment
    setup_environment()
    
    # Download model
    download_model()
    
    # Setup database
    setup_database()
    
    # Check dependencies
    check_system_dependencies()
    
    # Create systemd service (Linux only)
    if "--service" in sys.argv:
        create_systemd_service()
    
    print("\n" + "=" * 50)
    print("Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file with your configuration")
    print("2. Run: python main.py")
    print("3. Open http://localhost:8000 in your browser")
    print("\nFor production deployment:")
    print("- Use docker-compose up -d")
    print("- Or run with --service flag for systemd service")


if __name__ == "__main__":
    main()
