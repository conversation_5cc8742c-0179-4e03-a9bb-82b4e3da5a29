# KR Conveyor Belt Target Identification System

An AI-powered application for automatically detecting the clearing status of KR conveyor belts, performing quality control, and preventing SKU mixed code outflow.

## 🎯 Features

- **Real-time Detection**: Continuous monitoring of conveyor belt clearing status
- **AI-Powered Analysis**: YOLOv8-based object detection for SKU identification
- **Quality Control**: 100% detection of abnormal conveyor belt clearing behavior
- **Alert System**: Automated notifications for quality control issues
- **Web Dashboard**: Real-time monitoring interface with live camera feed
- **API Integration**: RESTful API for system integration
- **Data Logging**: Comprehensive audit trails and performance metrics

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Camera Feed   │───▶│ Image Processor │───▶│   AI Model      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Dashboard     │◀───│ Detection Engine│◀───│ SKU Classifier  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │  Alert System   │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │    Database     │
                       └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Camera device (USB/IP camera)
- 4GB+ RAM recommended
- CUDA-compatible GPU (optional, for better performance)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd AI-TargetIdenfify
   ```

2. **Run the setup script**
   ```bash
   python scripts/setup.py
   ```

3. **Configure the system**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the application**
   ```bash
   python main.py
   ```

5. **Access the dashboard**
   Open http://localhost:8000 in your browser

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Access the application
open http://localhost
```

## 📊 Detection Classes

The system detects the following conveyor belt states:

| Class | Description | Risk Level | Action Required |
|-------|-------------|------------|-----------------|
| `empty_belt` | Belt is completely clear | Normal | Ready for production |
| `cleared_belt` | Belt successfully cleared | Normal | Ready for next batch |
| `sku_present` | SKU items still on belt | Attention | Complete clearing |
| `mixed_sku` | Multiple SKU types detected | High | Review separation |
| `foreign_object` | Contamination detected | Critical | Stop production |

## 🔧 Configuration

### Environment Variables

```bash
# AI Model Settings
MODEL_PATH=models/yolov8n.pt
MODEL_CONFIDENCE=0.5
DEVICE=cpu  # or cuda, mps

# Camera Settings
CAMERA_INDEX=0
CAMERA_FPS=30

# Detection Settings
DETECTION_INTERVAL=1  # seconds
ALERT_THRESHOLD=0.8

# Email Alerts
EMAIL_ENABLED=true
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password
```

### Camera Setup

1. **USB Camera**: Set `CAMERA_INDEX=0` (or appropriate index)
2. **IP Camera**: Use RTSP URL in camera configuration
3. **Multiple Cameras**: Configure additional camera indices

## 📡 API Reference

### Detection Endpoints

- `GET /api/detection/status` - Get system status
- `POST /api/detection/start` - Start monitoring
- `POST /api/detection/stop` - Stop monitoring
- `POST /api/detection/detect` - Analyze uploaded image
- `GET /api/detection/live-frame` - Get live camera frame
- `GET /api/detection/history` - Get detection history

### Dashboard Endpoints

- `GET /api/dashboard/overview` - Dashboard overview
- `GET /api/dashboard/alerts` - Get alerts
- `POST /api/dashboard/alerts/{id}/acknowledge` - Acknowledge alert
- `POST /api/dashboard/alerts/{id}/resolve` - Resolve alert
- `GET /api/dashboard/statistics` - System statistics

### WebSocket

- `WS /ws` - Real-time updates and alerts

## 🎛️ Dashboard Features

### Live Monitoring
- Real-time camera feed with detection overlays
- System status indicators
- Performance metrics

### Alert Management
- Active alert notifications
- Alert acknowledgment and resolution
- Escalation procedures

### Analytics
- Detection history and trends
- Performance statistics
- Quality control metrics

## 🔍 Quality Control Process

1. **Continuous Monitoring**: Camera captures belt images every second
2. **AI Analysis**: YOLOv8 model analyzes each frame for objects
3. **Classification**: SKU classifier determines clearing status
4. **Risk Assessment**: System evaluates risk level based on detections
5. **Alert Generation**: Automatic alerts for quality issues
6. **Action Required**: Operators receive notifications for intervention

## 🚨 Alert System

### Alert Types

- **Critical**: Foreign objects, system failures (immediate action)
- **High**: Mixed SKU scenarios (review required)
- **Attention**: Incomplete clearing (monitoring needed)
- **Info**: Normal operations (logging only)

### Notification Channels

- Web dashboard notifications
- Email alerts (configurable)
- WebSocket real-time updates
- API webhooks (configurable)

## 📈 Performance Metrics

The system tracks:
- Detection accuracy and confidence
- Processing time per frame
- Alert response times
- System uptime and reliability
- Quality control effectiveness

## 🧪 Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run all tests
pytest

# Run specific test file
pytest tests/test_ai_model.py

# Run with coverage
pytest --cov=core tests/
```

## 🔧 Troubleshooting

### Common Issues

1. **Camera not detected**
   - Check camera connection and permissions
   - Verify CAMERA_INDEX in configuration
   - Test camera with `cv2.VideoCapture(0)`

2. **Model loading fails**
   - Ensure model file exists in models/ directory
   - Check available memory (4GB+ recommended)
   - Verify PyTorch installation

3. **Poor detection accuracy**
   - Adjust MODEL_CONFIDENCE threshold
   - Improve lighting conditions
   - Retrain model with specific data

4. **High CPU usage**
   - Enable GPU acceleration (CUDA/MPS)
   - Increase DETECTION_INTERVAL
   - Optimize image resolution

## 📚 Advanced Configuration

### Custom Model Training

1. Prepare training data with conveyor belt images
2. Annotate images with detection classes
3. Train YOLOv8 model with custom dataset
4. Replace model file and update class mappings

### Integration with Manufacturing Systems

- REST API for external system integration
- Database exports for reporting systems
- Webhook notifications for MES/ERP systems
- Custom alert routing and escalation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For technical support and questions:
- Create an issue in the repository
- Check the troubleshooting guide
- Review the API documentation

## 🔄 Version History

- **v1.0.0**: Initial release with core detection features
  - Real-time monitoring and alert system
  - Web dashboard and API integration
  - Comprehensive testing and documentation
