#!/usr/bin/env python3
"""
Run script for KR Conveyor Belt Target Identification System
Provides different run modes and configuration options
"""
import argparse
import sys
import os
import subprocess
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import torch
        import cv2
        import fastapi
        import uvicorn
        print("✓ All required dependencies are installed")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False


def setup_environment():
    """Setup environment and create necessary directories"""
    directories = ["data", "logs", "models", "static", "templates"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    # Copy environment file if it doesn't exist
    if not Path(".env").exists() and Path(".env.example").exists():
        import shutil
        shutil.copy(".env.example", ".env")
        print("Created .env file from .env.example")
        print("Please edit .env file with your configuration")


def run_development():
    """Run in development mode"""
    print("Starting KR Conveyor Belt Monitor in development mode...")
    
    # Set development environment variables
    os.environ["DEBUG"] = "true"
    os.environ["RELOAD"] = "true"
    os.environ["LOG_LEVEL"] = "DEBUG"
    
    # Import and run
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="debug"
    )


def run_production():
    """Run in production mode"""
    print("Starting KR Conveyor Belt Monitor in production mode...")
    
    # Set production environment variables
    os.environ["DEBUG"] = "false"
    os.environ["RELOAD"] = "false"
    os.environ["LOG_LEVEL"] = "INFO"
    
    # Import and run
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        workers=1,  # Single worker for camera access
        log_level="info"
    )


def run_tests():
    """Run test suite"""
    print("Running test suite...")
    
    try:
        result = subprocess.run(["pytest", "tests/", "-v"], check=True)
        print("✓ All tests passed")
        return True
    except subprocess.CalledProcessError:
        print("✗ Some tests failed")
        return False
    except FileNotFoundError:
        print("✗ pytest not found. Install with: pip install pytest")
        return False


def run_setup():
    """Run setup script"""
    print("Running setup...")
    
    try:
        subprocess.run([sys.executable, "scripts/setup.py"], check=True)
        print("✓ Setup completed successfully")
        return True
    except subprocess.CalledProcessError:
        print("✗ Setup failed")
        return False


def check_camera():
    """Check camera availability"""
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("✓ Camera is available")
            cap.release()
            return True
        else:
            print("⚠ Camera not detected (optional for file-based detection)")
            return False
    except ImportError:
        print("⚠ OpenCV not available for camera check")
        return False


def check_gpu():
    """Check GPU availability"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✓ CUDA GPU available: {gpu_name}")
            return True
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            print("✓ Apple Metal Performance Shaders (MPS) available")
            return True
        else:
            print("⚠ No GPU acceleration available (will use CPU)")
            return False
    except ImportError:
        print("⚠ PyTorch not available for GPU check")
        return False


def system_info():
    """Display system information"""
    print("KR Conveyor Belt Target Identification System")
    print("=" * 50)
    
    # Python version
    print(f"Python version: {sys.version}")
    
    # Check dependencies
    check_dependencies()
    
    # Check hardware
    check_camera()
    check_gpu()
    
    # Check configuration
    if Path(".env").exists():
        print("✓ Configuration file (.env) found")
    else:
        print("⚠ Configuration file (.env) not found")
    
    # Check model
    model_path = Path("models/yolov8n.pt")
    if model_path.exists():
        print(f"✓ Model file found: {model_path}")
    else:
        print("⚠ Model file not found (will download automatically)")
    
    print("\nSystem check completed.")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="KR Conveyor Belt Target Identification System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run.py                    # Run in development mode
  python run.py --production       # Run in production mode
  python run.py --test             # Run tests
  python run.py --setup            # Run setup
  python run.py --info             # Show system information
        """
    )
    
    parser.add_argument(
        "--production", "-p",
        action="store_true",
        help="Run in production mode"
    )
    
    parser.add_argument(
        "--test", "-t",
        action="store_true",
        help="Run test suite"
    )
    
    parser.add_argument(
        "--setup", "-s",
        action="store_true",
        help="Run setup script"
    )
    
    parser.add_argument(
        "--info", "-i",
        action="store_true",
        help="Show system information"
    )
    
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="Host to bind to (default: 0.0.0.0)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind to (default: 8000)"
    )
    
    args = parser.parse_args()
    
    # Setup environment
    setup_environment()
    
    # Handle different modes
    if args.info:
        system_info()
    elif args.setup:
        if not run_setup():
            sys.exit(1)
    elif args.test:
        if not run_tests():
            sys.exit(1)
    elif args.production:
        if not check_dependencies():
            sys.exit(1)
        run_production()
    else:
        # Default: development mode
        if not check_dependencies():
            sys.exit(1)
        run_development()


if __name__ == "__main__":
    main()
