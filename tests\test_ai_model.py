"""
Tests for AI Model Manager
"""
import pytest
import numpy as np
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

from core.ai_model import AIModelManager, DetectionResult


@pytest.fixture
def ai_model():
    """Create AI model instance for testing"""
    return AIModelManager()


@pytest.fixture
def sample_image():
    """Create sample image for testing"""
    return np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)


@pytest.fixture
def mock_yolo_model():
    """Mock YOLO model for testing"""
    mock_model = Mock()
    mock_result = Mock()
    mock_result.boxes = Mock()
    
    # Mock detection boxes
    mock_box = Mock()
    mock_box.xyxy = [Mock()]
    mock_box.xyxy[0].cpu.return_value.numpy.return_value = [100, 100, 200, 200]
    mock_box.conf = [Mock()]
    mock_box.conf[0].cpu.return_value.numpy.return_value = 0.8
    mock_box.cls = [Mock()]
    mock_box.cls[0].cpu.return_value.numpy.return_value = 2
    
    mock_result.boxes = [mock_box]
    mock_model.return_value = [mock_result]
    
    return mock_model


class TestAIModelManager:
    """Test cases for AI Model Manager"""
    
    @pytest.mark.asyncio
    async def test_model_initialization(self, ai_model):
        """Test model initialization"""
        assert ai_model.model is None
        assert not ai_model.is_loaded
        assert ai_model.device in ["cpu", "cuda", "mps"]
    
    @pytest.mark.asyncio
    async def test_load_model_success(self, ai_model, mock_yolo_model):
        """Test successful model loading"""
        with patch('core.ai_model.YOLO', return_value=mock_yolo_model):
            success = await ai_model.load_model("test_model.pt")
            
            assert success
            assert ai_model.is_loaded
            assert ai_model.model is not None
    
    @pytest.mark.asyncio
    async def test_load_model_failure(self, ai_model):
        """Test model loading failure"""
        with patch('core.ai_model.YOLO', side_effect=Exception("Model load failed")):
            success = await ai_model.load_model("nonexistent_model.pt")
            
            assert not success
            assert not ai_model.is_loaded
            assert ai_model.model is None
    
    @pytest.mark.asyncio
    async def test_detect_without_model(self, ai_model, sample_image):
        """Test detection without loaded model"""
        with pytest.raises(RuntimeError, match="Model not loaded"):
            await ai_model.detect(sample_image)
    
    @pytest.mark.asyncio
    async def test_detect_success(self, ai_model, sample_image, mock_yolo_model):
        """Test successful detection"""
        ai_model.model = mock_yolo_model
        ai_model.is_loaded = True
        
        with patch.object(ai_model, '_detect_sync') as mock_detect:
            expected_result = DetectionResult(
                boxes=[{
                    "x1": 100, "y1": 100, "x2": 200, "y2": 200,
                    "confidence": 0.8, "class_id": 2, "class_name": "sku_present",
                    "risk_level": "attention", "width": 100, "height": 100
                }],
                confidence=0.8,
                image_shape=(640, 640)
            )
            mock_detect.return_value = expected_result
            
            result = await ai_model.detect(sample_image)
            
            assert isinstance(result, DetectionResult)
            assert len(result.boxes) == 1
            assert result.confidence == 0.8
    
    @pytest.mark.asyncio
    async def test_detect_batch(self, ai_model, sample_image, mock_yolo_model):
        """Test batch detection"""
        ai_model.model = mock_yolo_model
        ai_model.is_loaded = True
        
        images = [sample_image, sample_image]
        
        with patch.object(ai_model, 'detect') as mock_detect:
            mock_detect.return_value = DetectionResult([], 0.0, (640, 640))
            
            results = await ai_model.detect_batch(images)
            
            assert len(results) == 2
            assert all(isinstance(r, DetectionResult) for r in results)
    
    def test_analyze_clearing_status_empty(self, ai_model):
        """Test clearing status analysis for empty belt"""
        detection_result = DetectionResult([], 0.0, (640, 640))
        
        analysis = ai_model.analyze_clearing_status(detection_result)
        
        assert analysis["status"] == "empty"
        assert analysis["risk_level"] == "normal"
        assert analysis["clearing_percentage"] == 100.0
    
    def test_analyze_clearing_status_contaminated(self, ai_model):
        """Test clearing status analysis for contaminated belt"""
        detection_result = DetectionResult([{
            "class_name": "foreign_object",
            "confidence": 0.9,
            "risk_level": "critical"
        }], 0.9, (640, 640))
        
        analysis = ai_model.analyze_clearing_status(detection_result)
        
        assert analysis["status"] == "contaminated"
        assert analysis["risk_level"] == "critical"
        assert analysis["clearing_percentage"] == 0.0
    
    def test_analyze_clearing_status_mixed_sku(self, ai_model):
        """Test clearing status analysis for mixed SKU"""
        detection_result = DetectionResult([{
            "class_name": "mixed_sku",
            "confidence": 0.7,
            "risk_level": "high"
        }], 0.7, (640, 640))
        
        analysis = ai_model.analyze_clearing_status(detection_result)
        
        assert analysis["status"] == "mixed_sku"
        assert analysis["risk_level"] == "high"
        assert analysis["clearing_percentage"] == 25.0
    
    def test_analyze_clearing_status_not_cleared(self, ai_model):
        """Test clearing status analysis for not cleared belt"""
        detection_result = DetectionResult([{
            "class_name": "sku_present",
            "confidence": 0.6,
            "risk_level": "attention"
        }], 0.6, (640, 640))
        
        analysis = ai_model.analyze_clearing_status(detection_result)
        
        assert analysis["status"] == "not_cleared"
        assert analysis["risk_level"] == "attention"
        assert analysis["clearing_percentage"] == 50.0
    
    def test_get_model_info_not_loaded(self, ai_model):
        """Test model info when not loaded"""
        info = ai_model.get_model_info()
        
        assert info["loaded"] is False
    
    def test_get_model_info_loaded(self, ai_model, mock_yolo_model):
        """Test model info when loaded"""
        ai_model.model = mock_yolo_model
        ai_model.is_loaded = True
        
        info = ai_model.get_model_info()
        
        assert info["loaded"] is True
        assert "device" in info
        assert "confidence_threshold" in info
        assert "class_names" in info
    
    @pytest.mark.asyncio
    async def test_cleanup(self, ai_model):
        """Test cleanup"""
        ai_model.model = Mock()
        ai_model.is_loaded = True
        
        await ai_model.cleanup()
        
        assert ai_model.model is None
        assert not ai_model.is_loaded


class TestDetectionResult:
    """Test cases for DetectionResult"""
    
    def test_detection_result_creation(self):
        """Test DetectionResult creation"""
        boxes = [{"x1": 0, "y1": 0, "x2": 100, "y2": 100}]
        result = DetectionResult(boxes, 0.8, (640, 640))
        
        assert result.boxes == boxes
        assert result.confidence == 0.8
        assert result.image_shape == (640, 640)
        assert result.timestamp > 0
    
    def test_detection_result_to_dict(self):
        """Test DetectionResult to_dict conversion"""
        boxes = [{"x1": 0, "y1": 0, "x2": 100, "y2": 100}]
        result = DetectionResult(boxes, 0.8, (640, 640))
        
        result_dict = result.to_dict()
        
        assert result_dict["boxes"] == boxes
        assert result_dict["confidence"] == 0.8
        assert result_dict["image_shape"] == (640, 640)
        assert result_dict["detection_count"] == 1
        assert "timestamp" in result_dict


if __name__ == "__main__":
    pytest.main([__file__])
