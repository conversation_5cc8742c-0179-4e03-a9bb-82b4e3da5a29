"""
Configuration settings for the KR Conveyor Belt Target Identification System
"""
import os
from pathlib import Path
from pydantic import BaseSettings, Field
from typing import List, Optional


class Settings(BaseSettings):
    """Application settings and configuration"""
    
    # Application Settings
    app_name: str = "KR Conveyor Belt Target Identification System"
    app_version: str = "1.0.0"
    debug: bool = Field(default=False, env="DEBUG")
    
    # Server Settings
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    reload: bool = Field(default=False, env="RELOAD")
    
    # AI Model Settings
    model_path: str = Field(default="models/yolov8n.pt", env="MODEL_PATH")
    model_confidence: float = Field(default=0.5, env="MODEL_CONFIDENCE")
    model_iou_threshold: float = Field(default=0.45, env="MODEL_IOU_THRESHOLD")
    device: str = Field(default="cpu", env="DEVICE")  # cpu, cuda, mps
    
    # Image Processing Settings
    image_width: int = Field(default=640, env="IMAGE_WIDTH")
    image_height: int = Field(default=640, env="IMAGE_HEIGHT")
    max_image_size: int = Field(default=10 * 1024 * 1024, env="MAX_IMAGE_SIZE")  # 10MB
    supported_formats: List[str] = ["jpg", "jpeg", "png", "bmp", "tiff"]
    
    # Camera Settings
    camera_index: int = Field(default=0, env="CAMERA_INDEX")
    camera_fps: int = Field(default=30, env="CAMERA_FPS")
    camera_resolution: tuple = (1920, 1080)
    
    # Detection Settings
    sku_classes: List[str] = [
        "empty_belt", "cleared_belt", "sku_present", "mixed_sku", "foreign_object"
    ]
    alert_threshold: float = Field(default=0.8, env="ALERT_THRESHOLD")
    detection_interval: int = Field(default=1, env="DETECTION_INTERVAL")  # seconds
    
    # Database Settings
    database_url: str = Field(default="sqlite:///./conveyor_monitoring.db", env="DATABASE_URL")
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # Logging Settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="logs/app.log", env="LOG_FILE")
    log_rotation: str = Field(default="1 day", env="LOG_ROTATION")
    log_retention: str = Field(default="30 days", env="LOG_RETENTION")
    
    # Alert Settings
    email_enabled: bool = Field(default=False, env="EMAIL_ENABLED")
    email_smtp_server: str = Field(default="", env="EMAIL_SMTP_SERVER")
    email_smtp_port: int = Field(default=587, env="EMAIL_SMTP_PORT")
    email_username: str = Field(default="", env="EMAIL_USERNAME")
    email_password: str = Field(default="", env="EMAIL_PASSWORD")
    alert_recipients: List[str] = []
    
    # File Paths
    base_dir: Path = Path(__file__).parent
    models_dir: Path = base_dir / "models"
    logs_dir: Path = base_dir / "logs"
    data_dir: Path = base_dir / "data"
    static_dir: Path = base_dir / "static"
    templates_dir: Path = base_dir / "templates"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Create directories if they don't exist
        for directory in [self.models_dir, self.logs_dir, self.data_dir, self.static_dir, self.templates_dir]:
            directory.mkdir(exist_ok=True)


# Global settings instance
settings = Settings()
