"""
Tests for API endpoints
"""
import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import Mock, AsyncMock, patch
import json

from main import app


@pytest.fixture
def client():
    """Create test client"""
    return TestClient(app)


@pytest.fixture
def mock_monitor():
    """Mock monitor instance"""
    monitor = Mock()
    
    # Mock detection engine
    monitor.detection_engine = Mock()
    monitor.detection_engine.get_status.return_value = {
        "is_monitoring": True,
        "camera_active": True,
        "model_loaded": True,
        "metrics": {
            "total_detections": 100,
            "successful_detections": 95,
            "failed_detections": 5,
            "average_processing_time": 1.2
        }
    }
    monitor.detection_engine.start_monitoring = AsyncMock(return_value=True)
    monitor.detection_engine.stop_monitoring = AsyncMock()
    monitor.detection_engine.detect_from_bytes = AsyncMock(return_value={
        "detection_result": {"detection_count": 2, "confidence": 0.8},
        "analysis": {"status": "not_cleared", "risk_level": "attention"},
        "image": "base64_image_data",
        "timestamp": 1234567890
    })
    monitor.detection_engine.get_live_frame = AsyncMock(return_value="base64_frame_data")
    monitor.detection_engine.get_recent_history.return_value = [
        {"timestamp": 1234567890, "analysis": {"status": "cleared"}, "detection_count": 0}
    ]
    
    # Mock AI model
    monitor.ai_model = Mock()
    monitor.ai_model.get_model_info.return_value = {
        "loaded": True,
        "device": "cpu",
        "model_type": "YOLOv8"
    }
    
    # Mock alert system
    monitor.alert_system = Mock()
    monitor.alert_system.get_active_alerts.return_value = []
    monitor.alert_system.get_alert_history.return_value = []
    monitor.alert_system.get_alert_statistics.return_value = {
        "total_alerts": 10,
        "active_alerts": 2
    }
    monitor.alert_system.acknowledge_alert = AsyncMock(return_value=True)
    monitor.alert_system.resolve_alert = AsyncMock(return_value=True)
    
    # Mock database
    monitor.database = Mock()
    monitor.database.get_statistics = AsyncMock(return_value={
        "detection_statistics": {"total_detections": 100},
        "alert_statistics": {"total_alerts": 10}
    })
    monitor.database.get_detection_results = AsyncMock(return_value=[])
    monitor.database.log_audit_event = AsyncMock(return_value=True)
    monitor.database.is_initialized = True
    
    return monitor


class TestDetectionAPI:
    """Test detection API endpoints"""
    
    def test_get_detection_status(self, client, mock_monitor):
        """Test GET /api/detection/status"""
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/detection/status")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "running"
        assert "detection_engine" in data
        assert "model_info" in data
    
    def test_get_detection_status_no_engine(self, client):
        """Test status endpoint without detection engine"""
        mock_monitor = Mock()
        mock_monitor.detection_engine = None
        
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/detection/status")
        
        assert response.status_code == 503
    
    def test_start_monitoring(self, client, mock_monitor):
        """Test POST /api/detection/start"""
        with patch('main.monitor', mock_monitor):
            response = client.post("/api/detection/start")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "running"
        mock_monitor.detection_engine.start_monitoring.assert_called_once()
    
    def test_start_monitoring_failure(self, client, mock_monitor):
        """Test start monitoring failure"""
        mock_monitor.detection_engine.start_monitoring.return_value = False
        
        with patch('main.monitor', mock_monitor):
            response = client.post("/api/detection/start")
        
        assert response.status_code == 500
    
    def test_stop_monitoring(self, client, mock_monitor):
        """Test POST /api/detection/stop"""
        with patch('main.monitor', mock_monitor):
            response = client.post("/api/detection/stop")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "stopped"
        mock_monitor.detection_engine.stop_monitoring.assert_called_once()
    
    def test_detect_from_upload(self, client, mock_monitor):
        """Test POST /api/detection/detect"""
        # Create fake image file
        fake_image = b"fake_image_data"
        
        with patch('main.monitor', mock_monitor):
            response = client.post(
                "/api/detection/detect",
                files={"file": ("test.jpg", fake_image, "image/jpeg")}
            )
        
        assert response.status_code == 200
        data = response.json()
        assert "detection_result" in data
        assert "analysis" in data
        assert "image" in data
    
    def test_detect_from_upload_invalid_file(self, client, mock_monitor):
        """Test detection with invalid file type"""
        fake_file = b"fake_text_data"
        
        with patch('main.monitor', mock_monitor):
            response = client.post(
                "/api/detection/detect",
                files={"file": ("test.txt", fake_file, "text/plain")}
            )
        
        assert response.status_code == 400
    
    def test_get_live_frame(self, client, mock_monitor):
        """Test GET /api/detection/live-frame"""
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/detection/live-frame")
        
        assert response.status_code == 200
        data = response.json()
        assert data["image"] == "base64_frame_data"
        assert "timestamp" in data
    
    def test_get_live_frame_no_camera(self, client, mock_monitor):
        """Test live frame without camera"""
        mock_monitor.detection_engine.get_live_frame.return_value = None
        
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/detection/live-frame")
        
        assert response.status_code == 503
    
    def test_get_detection_history(self, client, mock_monitor):
        """Test GET /api/detection/history"""
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/detection/history?limit=5")
        
        assert response.status_code == 200
        data = response.json()
        assert "history" in data
        assert "count" in data
    
    def test_get_detection_metrics(self, client, mock_monitor):
        """Test GET /api/detection/metrics"""
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/detection/metrics")
        
        assert response.status_code == 200
        data = response.json()
        assert "total_detections" in data
        assert "successful_detections" in data


class TestDashboardAPI:
    """Test dashboard API endpoints"""
    
    def test_get_dashboard_overview(self, client, mock_monitor):
        """Test GET /api/dashboard/overview"""
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/dashboard/overview")
        
        assert response.status_code == 200
        data = response.json()
        assert "system_status" in data
        assert "active_alerts" in data
        assert "recent_detections" in data
        assert "alert_statistics" in data
        assert "database_statistics" in data
    
    def test_get_alerts(self, client, mock_monitor):
        """Test GET /api/dashboard/alerts"""
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/dashboard/alerts")
        
        assert response.status_code == 200
        data = response.json()
        assert "alerts" in data
        assert "count" in data
    
    def test_get_active_alerts(self, client, mock_monitor):
        """Test GET /api/dashboard/alerts?active_only=true"""
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/dashboard/alerts?active_only=true")
        
        assert response.status_code == 200
        data = response.json()
        assert "alerts" in data
        mock_monitor.alert_system.get_active_alerts.assert_called_once()
    
    def test_acknowledge_alert(self, client, mock_monitor):
        """Test POST /api/dashboard/alerts/{alert_id}/acknowledge"""
        alert_id = "test_alert_123"
        
        with patch('main.monitor', mock_monitor):
            response = client.post(f"/api/dashboard/alerts/{alert_id}/acknowledge")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        mock_monitor.alert_system.acknowledge_alert.assert_called_once_with(alert_id, "anonymous")
    
    def test_acknowledge_alert_not_found(self, client, mock_monitor):
        """Test acknowledging non-existent alert"""
        mock_monitor.alert_system.acknowledge_alert.return_value = False
        alert_id = "nonexistent_alert"
        
        with patch('main.monitor', mock_monitor):
            response = client.post(f"/api/dashboard/alerts/{alert_id}/acknowledge")
        
        assert response.status_code == 404
    
    def test_resolve_alert(self, client, mock_monitor):
        """Test POST /api/dashboard/alerts/{alert_id}/resolve"""
        alert_id = "test_alert_123"
        
        with patch('main.monitor', mock_monitor):
            response = client.post(f"/api/dashboard/alerts/{alert_id}/resolve")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        mock_monitor.alert_system.resolve_alert.assert_called_once_with(alert_id, "anonymous")
    
    def test_resolve_alert_not_found(self, client, mock_monitor):
        """Test resolving non-existent alert"""
        mock_monitor.alert_system.resolve_alert.return_value = False
        alert_id = "nonexistent_alert"
        
        with patch('main.monitor', mock_monitor):
            response = client.post(f"/api/dashboard/alerts/{alert_id}/resolve")
        
        assert response.status_code == 404
    
    def test_get_system_statistics(self, client, mock_monitor):
        """Test GET /api/dashboard/statistics"""
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/dashboard/statistics?days=7")
        
        assert response.status_code == 200
        data = response.json()
        assert "detection_statistics" in data
        assert "alert_statistics" in data
    
    def test_get_detection_results(self, client, mock_monitor):
        """Test GET /api/dashboard/detection-results"""
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/dashboard/detection-results?limit=50")
        
        assert response.status_code == 200
        data = response.json()
        assert "results" in data
        assert "count" in data
    
    def test_get_system_health(self, client, mock_monitor):
        """Test GET /api/dashboard/system-health"""
        with patch('main.monitor', mock_monitor):
            response = client.get("/api/dashboard/system-health")
        
        assert response.status_code == 200
        data = response.json()
        assert "overall_status" in data
        assert "components" in data
        assert "ai_model" in data["components"]
        assert "detection_engine" in data["components"]
        assert "database" in data["components"]
        assert "alert_system" in data["components"]


class TestRootEndpoints:
    """Test root endpoints"""
    
    def test_root_endpoint(self, client):
        """Test GET /"""
        response = client.get("/")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]


if __name__ == "__main__":
    pytest.main([__file__])
