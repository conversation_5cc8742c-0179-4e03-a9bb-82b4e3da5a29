"""
Database Manager for Conveyor Belt Monitoring System
Handles data storage, retrieval, and management for detection results, alerts, and logs
"""
import asyncio
import sqlite3
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path
from loguru import logger
import aiosqlite

from config import settings


class DatabaseManager:
    """Manages database operations for the monitoring system"""
    
    def __init__(self):
        self.db_path = settings.database_url.replace("sqlite:///", "")
        self.connection_pool = None
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize database and create tables"""
        try:
            # Ensure database directory exists
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Create tables
            await self._create_tables()
            
            self.is_initialized = True
            logger.info(f"Database initialized: {self.db_path}")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {str(e)}")
            raise
    
    async def _create_tables(self):
        """Create database tables"""
        async with aiosqlite.connect(self.db_path) as db:
            # Detection results table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS detection_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    detection_count INTEGER NOT NULL,
                    confidence REAL NOT NULL,
                    status TEXT NOT NULL,
                    risk_level TEXT NOT NULL,
                    clearing_percentage REAL NOT NULL,
                    issues TEXT,
                    recommendations TEXT,
                    boxes TEXT,
                    image_path TEXT,
                    processing_time REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Alerts table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id TEXT PRIMARY KEY,
                    timestamp REAL NOT NULL,
                    alert_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    source TEXT NOT NULL,
                    data TEXT,
                    acknowledged BOOLEAN DEFAULT FALSE,
                    resolved BOOLEAN DEFAULT FALSE,
                    escalated BOOLEAN DEFAULT FALSE,
                    acknowledged_by TEXT,
                    resolved_by TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # System metrics table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    metric_unit TEXT,
                    metadata TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Audit logs table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    user_id TEXT,
                    action TEXT NOT NULL,
                    resource TEXT,
                    details TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # SKU information table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS sku_info (
                    sku_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    color TEXT,
                    shape TEXT,
                    dimensions TEXT,
                    weight REAL,
                    category TEXT,
                    active BOOLEAN DEFAULT TRUE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Performance statistics table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS performance_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    total_detections INTEGER DEFAULT 0,
                    successful_detections INTEGER DEFAULT 0,
                    failed_detections INTEGER DEFAULT 0,
                    average_processing_time REAL DEFAULT 0.0,
                    average_confidence REAL DEFAULT 0.0,
                    alerts_generated INTEGER DEFAULT 0,
                    uptime_percentage REAL DEFAULT 0.0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date)
                )
            """)
            
            # Create indexes for better performance
            await db.execute("CREATE INDEX IF NOT EXISTS idx_detection_timestamp ON detection_results(timestamp)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_detection_status ON detection_results(status)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_alerts_timestamp ON alerts(timestamp)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_alerts_type ON alerts(alert_type)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON system_metrics(timestamp)")
            await db.execute("CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_logs(timestamp)")
            
            await db.commit()
    
    async def store_detection_result(self, result_data: Dict[str, Any]) -> int:
        """Store detection result in database"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    INSERT INTO detection_results (
                        timestamp, detection_count, confidence, status, risk_level,
                        clearing_percentage, issues, recommendations, boxes, processing_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    result_data.get("timestamp"),
                    result_data.get("detection_count", 0),
                    result_data.get("confidence", 0.0),
                    result_data.get("status", "unknown"),
                    result_data.get("risk_level", "normal"),
                    result_data.get("clearing_percentage", 0.0),
                    result_data.get("issues", "[]"),
                    result_data.get("recommendations", "[]"),
                    result_data.get("boxes", "[]"),
                    result_data.get("processing_time", 0.0)
                ))
                
                await db.commit()
                return cursor.lastrowid
                
        except Exception as e:
            logger.error(f"Failed to store detection result: {str(e)}")
            raise
    
    async def store_alert(self, alert_data: Dict[str, Any]) -> bool:
        """Store alert in database"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT OR REPLACE INTO alerts (
                        id, timestamp, alert_type, title, message, source, data,
                        acknowledged, resolved, escalated
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    alert_data.get("id"),
                    alert_data.get("timestamp"),
                    alert_data.get("alert_type"),
                    alert_data.get("title"),
                    alert_data.get("message"),
                    alert_data.get("source"),
                    json.dumps(alert_data.get("data", {})),
                    alert_data.get("acknowledged", False),
                    alert_data.get("resolved", False),
                    alert_data.get("escalated", False)
                ))
                
                await db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to store alert: {str(e)}")
            return False
    
    async def update_alert_status(self, alert_id: str, status: str, user: str) -> bool:
        """Update alert status"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                if status == "acknowledged":
                    await db.execute("""
                        UPDATE alerts SET acknowledged = TRUE, acknowledged_by = ?
                        WHERE id = ?
                    """, (user, alert_id))
                elif status == "resolved":
                    await db.execute("""
                        UPDATE alerts SET resolved = TRUE, resolved_by = ?, acknowledged = TRUE
                        WHERE id = ?
                    """, (user, alert_id))
                
                await db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to update alert status: {str(e)}")
            return False
    
    async def store_metric(self, metric_name: str, value: float, unit: str = None, metadata: Dict = None) -> bool:
        """Store system metric"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT INTO system_metrics (timestamp, metric_name, metric_value, metric_unit, metadata)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    asyncio.get_event_loop().time(),
                    metric_name,
                    value,
                    unit,
                    json.dumps(metadata) if metadata else None
                ))
                
                await db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to store metric: {str(e)}")
            return False
    
    async def log_audit_event(self, user_id: str, action: str, resource: str = None, 
                             details: Dict = None, ip_address: str = None, user_agent: str = None) -> bool:
        """Log audit event"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT INTO audit_logs (timestamp, user_id, action, resource, details, ip_address, user_agent)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    asyncio.get_event_loop().time(),
                    user_id,
                    action,
                    resource,
                    json.dumps(details) if details else None,
                    ip_address,
                    user_agent
                ))
                
                await db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to log audit event: {str(e)}")
            return False
    
    async def get_detection_results(self, limit: int = 100, offset: int = 0, 
                                   start_time: float = None, end_time: float = None) -> List[Dict[str, Any]]:
        """Get detection results with optional filtering"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                query = "SELECT * FROM detection_results"
                params = []
                
                # Add time filtering
                if start_time or end_time:
                    conditions = []
                    if start_time:
                        conditions.append("timestamp >= ?")
                        params.append(start_time)
                    if end_time:
                        conditions.append("timestamp <= ?")
                        params.append(end_time)
                    query += " WHERE " + " AND ".join(conditions)
                
                query += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
                params.extend([limit, offset])
                
                async with db.execute(query, params) as cursor:
                    rows = await cursor.fetchall()
                    columns = [description[0] for description in cursor.description]
                    
                    results = []
                    for row in rows:
                        result = dict(zip(columns, row))
                        # Parse JSON fields
                        for field in ["issues", "recommendations", "boxes"]:
                            if result.get(field):
                                try:
                                    result[field] = json.loads(result[field])
                                except:
                                    result[field] = []
                        results.append(result)
                    
                    return results
                    
        except Exception as e:
            logger.error(f"Failed to get detection results: {str(e)}")
            return []
    
    async def get_alerts(self, limit: int = 50, active_only: bool = False) -> List[Dict[str, Any]]:
        """Get alerts with optional filtering"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                query = "SELECT * FROM alerts"
                params = []
                
                if active_only:
                    query += " WHERE resolved = FALSE"
                
                query += " ORDER BY timestamp DESC LIMIT ?"
                params.append(limit)
                
                async with db.execute(query, params) as cursor:
                    rows = await cursor.fetchall()
                    columns = [description[0] for description in cursor.description]
                    
                    results = []
                    for row in rows:
                        result = dict(zip(columns, row))
                        # Parse JSON data field
                        if result.get("data"):
                            try:
                                result["data"] = json.loads(result["data"])
                            except:
                                result["data"] = {}
                        results.append(result)
                    
                    return results
                    
        except Exception as e:
            logger.error(f"Failed to get alerts: {str(e)}")
            return []
    
    async def get_system_metrics(self, metric_name: str = None, limit: int = 100, 
                                start_time: float = None, end_time: float = None) -> List[Dict[str, Any]]:
        """Get system metrics with optional filtering"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                query = "SELECT * FROM system_metrics"
                params = []
                conditions = []
                
                if metric_name:
                    conditions.append("metric_name = ?")
                    params.append(metric_name)
                
                if start_time:
                    conditions.append("timestamp >= ?")
                    params.append(start_time)
                
                if end_time:
                    conditions.append("timestamp <= ?")
                    params.append(end_time)
                
                if conditions:
                    query += " WHERE " + " AND ".join(conditions)
                
                query += " ORDER BY timestamp DESC LIMIT ?"
                params.append(limit)
                
                async with db.execute(query, params) as cursor:
                    rows = await cursor.fetchall()
                    columns = [description[0] for description in cursor.description]
                    
                    results = []
                    for row in rows:
                        result = dict(zip(columns, row))
                        # Parse metadata JSON
                        if result.get("metadata"):
                            try:
                                result["metadata"] = json.loads(result["metadata"])
                            except:
                                result["metadata"] = {}
                        results.append(result)
                    
                    return results
                    
        except Exception as e:
            logger.error(f"Failed to get system metrics: {str(e)}")
            return []
    
    async def get_statistics(self, days: int = 7) -> Dict[str, Any]:
        """Get system statistics for the specified number of days"""
        try:
            start_time = asyncio.get_event_loop().time() - (days * 24 * 3600)
            
            async with aiosqlite.connect(self.db_path) as db:
                # Detection statistics
                async with db.execute("""
                    SELECT 
                        COUNT(*) as total_detections,
                        AVG(confidence) as avg_confidence,
                        AVG(clearing_percentage) as avg_clearing,
                        COUNT(CASE WHEN risk_level = 'critical' THEN 1 END) as critical_detections,
                        COUNT(CASE WHEN risk_level = 'high' THEN 1 END) as high_risk_detections
                    FROM detection_results 
                    WHERE timestamp >= ?
                """, (start_time,)) as cursor:
                    detection_stats = dict(zip([d[0] for d in cursor.description], await cursor.fetchone()))
                
                # Alert statistics
                async with db.execute("""
                    SELECT 
                        COUNT(*) as total_alerts,
                        COUNT(CASE WHEN alert_type = 'critical' THEN 1 END) as critical_alerts,
                        COUNT(CASE WHEN alert_type = 'high' THEN 1 END) as high_alerts,
                        COUNT(CASE WHEN resolved = TRUE THEN 1 END) as resolved_alerts
                    FROM alerts 
                    WHERE timestamp >= ?
                """, (start_time,)) as cursor:
                    alert_stats = dict(zip([d[0] for d in cursor.description], await cursor.fetchone()))
                
                return {
                    "period_days": days,
                    "detection_statistics": detection_stats,
                    "alert_statistics": alert_stats,
                    "generated_at": asyncio.get_event_loop().time()
                }
                
        except Exception as e:
            logger.error(f"Failed to get statistics: {str(e)}")
            return {}
    
    async def cleanup_old_data(self, days_to_keep: int = 30) -> bool:
        """Clean up old data beyond retention period"""
        try:
            cutoff_time = asyncio.get_event_loop().time() - (days_to_keep * 24 * 3600)
            
            async with aiosqlite.connect(self.db_path) as db:
                # Clean old detection results
                await db.execute("DELETE FROM detection_results WHERE timestamp < ?", (cutoff_time,))
                
                # Clean old resolved alerts
                await db.execute("DELETE FROM alerts WHERE timestamp < ? AND resolved = TRUE", (cutoff_time,))
                
                # Clean old metrics
                await db.execute("DELETE FROM system_metrics WHERE timestamp < ?", (cutoff_time,))
                
                # Clean old audit logs
                await db.execute("DELETE FROM audit_logs WHERE timestamp < ?", (cutoff_time,))
                
                await db.commit()
                
            logger.info(f"Cleaned up data older than {days_to_keep} days")
            return True
            
        except Exception as e:
            logger.error(f"Data cleanup failed: {str(e)}")
            return False
    
    async def close(self):
        """Close database connections"""
        self.is_initialized = False
        logger.info("Database connections closed")
