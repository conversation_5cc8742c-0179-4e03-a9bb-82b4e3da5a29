"""
Logging configuration for the KR Conveyor Belt Target Identification System
"""
import sys
from pathlib import Path
from loguru import logger

from config import settings


def setup_logging():
    """Setup logging configuration"""
    # Remove default logger
    logger.remove()
    
    # Console logging
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # File logging
    logger.add(
        settings.log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation=settings.log_rotation,
        retention=settings.log_retention,
        compression="zip"
    )
    
    # Error file logging
    error_log_file = Path(settings.log_file).parent / "error.log"
    logger.add(
        error_log_file,
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation=settings.log_rotation,
        retention=settings.log_retention,
        compression="zip"
    )
    
    logger.info("Logging system initialized")


def get_logger(name: str):
    """Get a logger instance for a specific module"""
    return logger.bind(name=name)
