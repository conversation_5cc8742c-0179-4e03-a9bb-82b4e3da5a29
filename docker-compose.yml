version: '3.8'

services:
  conveyor-monitor:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - /dev/video0:/dev/video0  # Camera device (Linux)
    environment:
      - DEBUG=false
      - DATABASE_URL=sqlite:///./data/conveyor_monitoring.db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    restart: unless-stopped
    privileged: true  # Required for camera access
    
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - conveyor-monitor
    restart: unless-stopped

volumes:
  redis_data:
