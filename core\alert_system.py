"""
Alert and Notification System for Conveyor Belt Monitoring
Handles real-time alerts, notifications, and escalation procedures
"""
import asyncio
import smtplib
import json
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from loguru import logger
import time

from config import settings
from core.database import DatabaseManager


@dataclass
class Alert:
    """Alert data structure"""
    id: str
    timestamp: float
    alert_type: str  # critical, high, attention, info
    title: str
    message: str
    source: str
    data: Dict[str, Any]
    acknowledged: bool = False
    resolved: bool = False
    escalated: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class AlertSystem:
    """Manages alerts and notifications for the monitoring system"""
    
    def __init__(self, database: DatabaseManager):
        self.database = database
        self.active_alerts = {}
        self.alert_history = []
        self.max_history = 1000
        self.websocket_connections = set()
        
        # Alert thresholds and rules
        self.alert_rules = {
            "critical": {
                "foreign_object": {"threshold": 0.5, "cooldown": 30},
                "system_failure": {"threshold": 0.0, "cooldown": 60}
            },
            "high": {
                "mixed_sku": {"threshold": 0.7, "cooldown": 120},
                "contamination": {"threshold": 0.6, "cooldown": 180}
            },
            "attention": {
                "not_cleared": {"threshold": 0.5, "cooldown": 300},
                "low_confidence": {"threshold": 0.3, "cooldown": 600}
            }
        }
        
        # Escalation settings
        self.escalation_times = {
            "critical": 300,    # 5 minutes
            "high": 900,        # 15 minutes
            "attention": 1800   # 30 minutes
        }
        
        # Email settings
        self.email_enabled = settings.email_enabled
        self.smtp_server = settings.email_smtp_server
        self.smtp_port = settings.email_smtp_port
        self.email_username = settings.email_username
        self.email_password = settings.email_password
        self.alert_recipients = settings.alert_recipients
    
    async def process_detection_result(self, detection_result: Dict, analysis: Dict):
        """Process detection results and generate alerts if needed"""
        try:
            # Check for critical conditions
            await self._check_critical_alerts(detection_result, analysis)
            
            # Check for high priority conditions
            await self._check_high_priority_alerts(detection_result, analysis)
            
            # Check for attention conditions
            await self._check_attention_alerts(detection_result, analysis)
            
            # Process escalations
            await self._process_escalations()
            
        except Exception as e:
            logger.error(f"Alert processing failed: {str(e)}")
    
    async def _check_critical_alerts(self, detection_result: Dict, analysis: Dict):
        """Check for critical alert conditions"""
        # Foreign object detection
        if analysis.get("contamination_detected", False):
            await self._create_alert(
                alert_type="critical",
                title="Foreign Object Detected",
                message="Foreign object contamination detected on conveyor belt. Immediate action required.",
                source="contamination_detector",
                data={
                    "detection_result": detection_result,
                    "analysis": analysis,
                    "action_required": "stop_production"
                }
            )
        
        # System failure detection
        if detection_result.get("error"):
            await self._create_alert(
                alert_type="critical",
                title="System Detection Failure",
                message=f"Detection system failure: {detection_result.get('error')}",
                source="system_monitor",
                data={"error": detection_result.get("error")}
            )
    
    async def _check_high_priority_alerts(self, detection_result: Dict, analysis: Dict):
        """Check for high priority alert conditions"""
        # Mixed SKU detection
        if analysis.get("mixed_sku_detected", False):
            await self._create_alert(
                alert_type="high",
                title="Mixed SKU Detected",
                message="Mixed SKU scenario detected. Quality control review required.",
                source="sku_classifier",
                data={
                    "sku_distribution": analysis.get("sku_counts", {}),
                    "coverage_percentage": analysis.get("coverage_percentage", 0),
                    "dominant_sku": analysis.get("dominant_sku")
                }
            )
        
        # High confidence contamination
        contamination_confidence = detection_result.get("confidence", 0)
        if contamination_confidence > 0.8 and analysis.get("risk_assessment") == "high":
            await self._create_alert(
                alert_type="high",
                title="High Confidence Contamination",
                message=f"High confidence ({contamination_confidence:.1%}) contamination detected.",
                source="ai_model",
                data={"confidence": contamination_confidence, "boxes": detection_result.get("boxes", [])}
            )
    
    async def _check_attention_alerts(self, detection_result: Dict, analysis: Dict):
        """Check for attention-level alert conditions"""
        # Belt not cleared
        if analysis.get("clearing_status") == "not_cleared":
            clearing_percentage = analysis.get("clearing_percentage", 0)
            await self._create_alert(
                alert_type="attention",
                title="Belt Not Fully Cleared",
                message=f"Belt clearing at {clearing_percentage:.1f}%. Complete clearing recommended.",
                source="clearing_monitor",
                data={
                    "clearing_percentage": clearing_percentage,
                    "recommendations": analysis.get("recommendations", [])
                }
            )
        
        # Low detection confidence
        confidence = detection_result.get("confidence", 1.0)
        if confidence < 0.5 and detection_result.get("detection_count", 0) > 0:
            await self._create_alert(
                alert_type="attention",
                title="Low Detection Confidence",
                message=f"Detection confidence below threshold ({confidence:.1%}). Review may be needed.",
                source="confidence_monitor",
                data={"confidence": confidence, "threshold": 0.5}
            )
    
    async def _create_alert(self, alert_type: str, title: str, message: str, source: str, data: Dict):
        """Create and process a new alert"""
        try:
            # Check cooldown period
            if not self._check_cooldown(alert_type, source):
                return
            
            # Generate alert ID
            alert_id = f"{alert_type}_{source}_{int(time.time())}"
            
            # Create alert object
            alert = Alert(
                id=alert_id,
                timestamp=time.time(),
                alert_type=alert_type,
                title=title,
                message=message,
                source=source,
                data=data
            )
            
            # Store alert
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            # Trim history if needed
            if len(self.alert_history) > self.max_history:
                self.alert_history = self.alert_history[-self.max_history:]
            
            # Store in database
            await self.database.store_alert(alert.to_dict())
            
            # Send notifications
            await self._send_notifications(alert)
            
            # Log alert
            logger.warning(f"Alert created: {alert_type.upper()} - {title}")
            
        except Exception as e:
            logger.error(f"Alert creation failed: {str(e)}")
    
    def _check_cooldown(self, alert_type: str, source: str) -> bool:
        """Check if alert is within cooldown period"""
        try:
            cooldown = self.alert_rules.get(alert_type, {}).get(source, {}).get("cooldown", 0)
            if cooldown == 0:
                return True
            
            # Check recent alerts of same type and source
            current_time = time.time()
            for alert in reversed(self.alert_history):
                if (alert.alert_type == alert_type and 
                    alert.source == source and 
                    current_time - alert.timestamp < cooldown):
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Cooldown check failed: {str(e)}")
            return True
    
    async def _send_notifications(self, alert: Alert):
        """Send notifications for an alert"""
        try:
            # Send WebSocket notifications
            await self._send_websocket_notification(alert)
            
            # Send email notifications for critical and high priority alerts
            if alert.alert_type in ["critical", "high"] and self.email_enabled:
                await self._send_email_notification(alert)
            
        except Exception as e:
            logger.error(f"Notification sending failed: {str(e)}")
    
    async def _send_websocket_notification(self, alert: Alert):
        """Send real-time WebSocket notification"""
        try:
            notification_data = {
                "type": "alert",
                "alert": alert.to_dict()
            }
            
            # Send to all connected WebSocket clients
            if self.websocket_connections:
                message = json.dumps(notification_data)
                disconnected = set()
                
                for websocket in self.websocket_connections:
                    try:
                        await websocket.send_text(message)
                    except Exception:
                        disconnected.add(websocket)
                
                # Remove disconnected clients
                self.websocket_connections -= disconnected
                
        except Exception as e:
            logger.error(f"WebSocket notification failed: {str(e)}")
    
    async def _send_email_notification(self, alert: Alert):
        """Send email notification"""
        try:
            if not self.alert_recipients:
                return
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = self.email_username
            msg['To'] = ', '.join(self.alert_recipients)
            msg['Subject'] = f"[{alert.alert_type.upper()}] KR Conveyor Belt Alert: {alert.title}"
            
            # Create email body
            body = self._create_email_body(alert)
            msg.attach(MIMEText(body, 'html'))
            
            # Send email
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._send_email_sync, msg)
            
            logger.info(f"Email notification sent for alert: {alert.id}")
            
        except Exception as e:
            logger.error(f"Email notification failed: {str(e)}")
    
    def _send_email_sync(self, msg: MIMEMultipart):
        """Synchronous email sending"""
        try:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.email_username, self.email_password)
            
            text = msg.as_string()
            server.sendmail(self.email_username, self.alert_recipients, text)
            server.quit()
            
        except Exception as e:
            logger.error(f"SMTP sending failed: {str(e)}")
    
    def _create_email_body(self, alert: Alert) -> str:
        """Create HTML email body for alert"""
        timestamp_str = datetime.fromtimestamp(alert.timestamp).strftime("%Y-%m-%d %H:%M:%S")
        
        color_map = {
            "critical": "#dc3545",
            "high": "#fd7e14", 
            "attention": "#ffc107",
            "info": "#17a2b8"
        }
        
        color = color_map.get(alert.alert_type, "#6c757d")
        
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
            <div style="border-left: 4px solid {color}; padding-left: 20px;">
                <h2 style="color: {color}; margin-top: 0;">
                    {alert.alert_type.upper()} ALERT
                </h2>
                <h3>{alert.title}</h3>
                <p><strong>Time:</strong> {timestamp_str}</p>
                <p><strong>Source:</strong> {alert.source}</p>
                <p><strong>Message:</strong> {alert.message}</p>
                
                <h4>Additional Information:</h4>
                <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px;">
{json.dumps(alert.data, indent=2)}
                </pre>
                
                <hr>
                <p style="color: #6c757d; font-size: 12px;">
                    This is an automated alert from the KR Conveyor Belt Target Identification System.
                    Please acknowledge this alert in the system dashboard.
                </p>
            </div>
        </body>
        </html>
        """
    
    async def _process_escalations(self):
        """Process alert escalations based on time thresholds"""
        try:
            current_time = time.time()
            
            for alert_id, alert in self.active_alerts.items():
                if alert.escalated or alert.acknowledged or alert.resolved:
                    continue
                
                escalation_time = self.escalation_times.get(alert.alert_type, 0)
                if escalation_time > 0 and current_time - alert.timestamp > escalation_time:
                    await self._escalate_alert(alert)
                    
        except Exception as e:
            logger.error(f"Escalation processing failed: {str(e)}")
    
    async def _escalate_alert(self, alert: Alert):
        """Escalate an unacknowledged alert"""
        try:
            alert.escalated = True
            
            # Create escalation alert
            await self._create_alert(
                alert_type="critical",
                title=f"ESCALATED: {alert.title}",
                message=f"Alert {alert.id} has been escalated due to lack of acknowledgment.",
                source="escalation_system",
                data={
                    "original_alert": alert.to_dict(),
                    "escalation_reason": "timeout"
                }
            )
            
            logger.warning(f"Alert escalated: {alert.id}")
            
        except Exception as e:
            logger.error(f"Alert escalation failed: {str(e)}")
    
    async def acknowledge_alert(self, alert_id: str, user: str = "system") -> bool:
        """Acknowledge an alert"""
        try:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].acknowledged = True
                
                # Update in database
                await self.database.update_alert_status(alert_id, "acknowledged", user)
                
                logger.info(f"Alert acknowledged: {alert_id} by {user}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Alert acknowledgment failed: {str(e)}")
            return False
    
    async def resolve_alert(self, alert_id: str, user: str = "system") -> bool:
        """Resolve an alert"""
        try:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.acknowledged = True
                alert.resolved = True
                
                # Remove from active alerts
                del self.active_alerts[alert_id]
                
                # Update in database
                await self.database.update_alert_status(alert_id, "resolved", user)
                
                logger.info(f"Alert resolved: {alert_id} by {user}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Alert resolution failed: {str(e)}")
            return False
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all active alerts"""
        return [alert.to_dict() for alert in self.active_alerts.values()]
    
    def get_alert_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent alert history"""
        return [alert.to_dict() for alert in self.alert_history[-limit:]]
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Get alert statistics"""
        total_alerts = len(self.alert_history)
        active_count = len(self.active_alerts)
        
        # Count by type
        type_counts = {}
        for alert in self.alert_history:
            alert_type = alert.alert_type
            type_counts[alert_type] = type_counts.get(alert_type, 0) + 1
        
        return {
            "total_alerts": total_alerts,
            "active_alerts": active_count,
            "alerts_by_type": type_counts,
            "escalated_alerts": sum(1 for alert in self.active_alerts.values() if alert.escalated),
            "unacknowledged_alerts": sum(1 for alert in self.active_alerts.values() if not alert.acknowledged)
        }
    
    def add_websocket_connection(self, websocket):
        """Add WebSocket connection for real-time notifications"""
        self.websocket_connections.add(websocket)
    
    def remove_websocket_connection(self, websocket):
        """Remove WebSocket connection"""
        self.websocket_connections.discard(websocket)
    
    async def cleanup(self):
        """Cleanup alert system resources"""
        # Close all WebSocket connections
        for websocket in self.websocket_connections.copy():
            try:
                await websocket.close()
            except:
                pass
        
        self.websocket_connections.clear()
        logger.info("Alert System cleaned up")
