# API Reference

## Base URL
```
http://localhost:8000
```

## Authentication
Currently, the API uses basic authentication headers. In production, implement proper authentication tokens.

## Detection API

### Get System Status
```http
GET /api/detection/status
```

**Response:**
```json
{
  "status": "running",
  "detection_engine": {
    "is_monitoring": true,
    "camera_active": true,
    "model_loaded": true,
    "metrics": {
      "total_detections": 1250,
      "successful_detections": 1200,
      "failed_detections": 50,
      "average_processing_time": 1.2
    }
  },
  "model_info": {
    "loaded": true,
    "device": "cpu",
    "model_type": "YOLOv8"
  }
}
```

### Start Monitoring
```http
POST /api/detection/start
```

**Response:**
```json
{
  "message": "Monitoring started successfully",
  "status": "running"
}
```

### Stop Monitoring
```http
POST /api/detection/stop
```

**Response:**
```json
{
  "message": "Monitoring stopped successfully",
  "status": "stopped"
}
```

### Detect from Image Upload
```http
POST /api/detection/detect
Content-Type: multipart/form-data
```

**Parameters:**
- `file`: Image file (JPEG, PNG, BMP, TIFF)

**Response:**
```json
{
  "detection_result": {
    "boxes": [
      {
        "x1": 100, "y1": 100, "x2": 200, "y2": 200,
        "confidence": 0.85,
        "class_name": "sku_present",
        "risk_level": "attention"
      }
    ],
    "confidence": 0.85,
    "detection_count": 1
  },
  "analysis": {
    "status": "not_cleared",
    "risk_level": "attention",
    "clearing_percentage": 75.0,
    "issues": ["SKU detected with 85% confidence"],
    "recommendations": ["Complete clearing before next batch"]
  },
  "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "timestamp": **********.123
}
```

### Get Live Frame
```http
GET /api/detection/live-frame
```

**Response:**
```json
{
  "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "timestamp": **********.123
}
```

### Get Detection History
```http
GET /api/detection/history?limit=10
```

**Parameters:**
- `limit` (optional): Number of records to return (default: 10)

**Response:**
```json
{
  "history": [
    {
      "timestamp": **********.123,
      "analysis": {
        "status": "cleared",
        "risk_level": "normal"
      },
      "detection_count": 0
    }
  ],
  "count": 1
}
```

## Dashboard API

### Get Dashboard Overview
```http
GET /api/dashboard/overview
```

**Response:**
```json
{
  "system_status": { /* detection status */ },
  "active_alerts": [ /* active alerts */ ],
  "recent_detections": [ /* recent history */ ],
  "alert_statistics": {
    "total_alerts": 45,
    "active_alerts": 3,
    "critical_alerts": 1
  },
  "database_statistics": {
    "detection_statistics": {
      "total_detections": 1250,
      "avg_confidence": 0.82
    }
  }
}
```

### Get Alerts
```http
GET /api/dashboard/alerts?active_only=false&limit=50
```

**Parameters:**
- `active_only` (optional): Return only active alerts (default: false)
- `limit` (optional): Number of alerts to return (default: 50)

**Response:**
```json
{
  "alerts": [
    {
      "id": "alert_123",
      "timestamp": **********.123,
      "alert_type": "critical",
      "title": "Foreign Object Detected",
      "message": "Foreign object contamination detected on conveyor belt",
      "source": "contamination_detector",
      "acknowledged": false,
      "resolved": false
    }
  ],
  "count": 1
}
```

### Acknowledge Alert
```http
POST /api/dashboard/alerts/{alert_id}/acknowledge
```

**Headers:**
- `X-User-ID`: User identifier

**Response:**
```json
{
  "message": "Alert acknowledged successfully"
}
```

### Resolve Alert
```http
POST /api/dashboard/alerts/{alert_id}/resolve
```

**Headers:**
- `X-User-ID`: User identifier

**Response:**
```json
{
  "message": "Alert resolved successfully"
}
```

### Get System Statistics
```http
GET /api/dashboard/statistics?days=7
```

**Parameters:**
- `days` (optional): Number of days for statistics (default: 7)

**Response:**
```json
{
  "period_days": 7,
  "detection_statistics": {
    "total_detections": 1250,
    "avg_confidence": 0.82,
    "avg_clearing": 85.5,
    "critical_detections": 15,
    "high_risk_detections": 45
  },
  "alert_statistics": {
    "total_alerts": 45,
    "critical_alerts": 5,
    "resolved_alerts": 40
  }
}
```

### Get Detection Results
```http
GET /api/dashboard/detection-results?limit=100&offset=0&start_time=**********&end_time=1234567999
```

**Parameters:**
- `limit` (optional): Number of results (default: 100)
- `offset` (optional): Offset for pagination (default: 0)
- `start_time` (optional): Start timestamp filter
- `end_time` (optional): End timestamp filter

**Response:**
```json
{
  "results": [
    {
      "id": 1,
      "timestamp": **********.123,
      "detection_count": 2,
      "confidence": 0.85,
      "status": "not_cleared",
      "risk_level": "attention",
      "clearing_percentage": 75.0,
      "issues": ["SKU detected"],
      "recommendations": ["Complete clearing"]
    }
  ],
  "count": 1
}
```

### Get System Health
```http
GET /api/dashboard/system-health
```

**Response:**
```json
{
  "overall_status": "healthy",
  "components": {
    "ai_model": {
      "status": "healthy",
      "details": { "loaded": true, "device": "cpu" }
    },
    "detection_engine": {
      "status": "healthy",
      "details": { "is_monitoring": true }
    },
    "database": {
      "status": "healthy",
      "details": "Connected"
    },
    "alert_system": {
      "status": "healthy",
      "details": { "active_alerts": 2 }
    }
  },
  "timestamp": **********.123
}
```

## WebSocket API

### Connection
```javascript
const ws = new WebSocket('ws://localhost:8000/ws');
```

### Message Types

#### Status Update
```json
{
  "type": "status",
  "timestamp": **********.123,
  "system_status": "running",
  "active_connections": 3
}
```

#### Alert Notification
```json
{
  "type": "alert",
  "alert": {
    "id": "alert_123",
    "alert_type": "critical",
    "title": "Foreign Object Detected",
    "message": "Immediate action required",
    "timestamp": **********.123
  }
}
```

## Error Responses

### Standard Error Format
```json
{
  "detail": "Error message description"
}
```

### HTTP Status Codes
- `200`: Success
- `400`: Bad Request (invalid parameters)
- `404`: Not Found (resource doesn't exist)
- `500`: Internal Server Error
- `503`: Service Unavailable (component not initialized)

## Rate Limiting
- API endpoints: 10 requests/second per IP
- File uploads: 5 requests/minute per IP
- WebSocket connections: 10 concurrent per IP
