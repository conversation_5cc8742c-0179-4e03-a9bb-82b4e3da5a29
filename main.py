"""
Main application entry point for KR Conveyor Belt Target Identification System
"""
import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from loguru import logger
import json

from config import settings
from core.ai_model import AIModelManager
from core.image_processor import ImageProcessor
from core.detection_engine import DetectionEngine
from core.alert_system import AlertSystem
from core.database import DatabaseManager
from api.routes import detection_router, dashboard_router
from utils.logger import setup_logging


class ConveyorBeltMonitor:
    """Main application class for conveyor belt monitoring"""
    
    def __init__(self):
        self.ai_model = None
        self.image_processor = None
        self.detection_engine = None
        self.alert_system = None
        self.database = None
        self.websocket_connections = set()
    
    async def initialize(self):
        """Initialize all system components"""
        logger.info("Initializing Conveyor Belt Monitoring System...")
        
        # Initialize database
        self.database = DatabaseManager()
        await self.database.initialize()
        
        # Initialize AI model
        self.ai_model = AIModelManager()
        await self.ai_model.load_model()
        
        # Initialize image processor
        self.image_processor = ImageProcessor()
        
        # Initialize detection engine
        self.detection_engine = DetectionEngine(
            ai_model=self.ai_model,
            image_processor=self.image_processor,
            database=self.database
        )
        
        # Initialize alert system
        self.alert_system = AlertSystem(database=self.database)
        
        logger.info("System initialization completed successfully")
    
    async def cleanup(self):
        """Cleanup system resources"""
        logger.info("Shutting down Conveyor Belt Monitoring System...")
        
        if self.database:
            await self.database.close()
        
        # Close all websocket connections
        for websocket in self.websocket_connections.copy():
            await websocket.close()
        
        logger.info("System shutdown completed")


# Global monitor instance
monitor = ConveyorBeltMonitor()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    setup_logging()
    await monitor.initialize()
    yield
    # Shutdown
    await monitor.cleanup()


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="AI-powered conveyor belt monitoring and SKU detection system",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory=settings.static_dir), name="static")

# Setup templates
templates = Jinja2Templates(directory=settings.templates_dir)

# Include routers
app.include_router(detection_router, prefix="/api/detection", tags=["detection"])
app.include_router(dashboard_router, prefix="/api/dashboard", tags=["dashboard"])


@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint serving the main dashboard"""
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>KR Conveyor Belt Monitor</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .status-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
            .btn:hover { background: #2980b9; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>KR Conveyor Belt Target Identification System</h1>
                <p>Real-time monitoring and SKU detection for quality control</p>
            </div>
            <div class="status-grid">
                <div class="status-card">
                    <h3>System Status</h3>
                    <p>Status: <span id="system-status">Initializing...</span></p>
                    <a href="/dashboard" class="btn">Open Dashboard</a>
                </div>
                <div class="status-card">
                    <h3>Detection Engine</h3>
                    <p>Model: YOLOv8</p>
                    <p>Confidence: 50%</p>
                    <a href="/api/detection/status" class="btn">Check Status</a>
                </div>
                <div class="status-card">
                    <h3>Quick Actions</h3>
                    <a href="/api/docs" class="btn">API Documentation</a><br><br>
                    <a href="/dashboard" class="btn">Live Dashboard</a>
                </div>
            </div>
        </div>
        <script>
            // Simple status check
            fetch('/api/detection/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('system-status').textContent = data.status || 'Running';
                })
                .catch(error => {
                    document.getElementById('system-status').textContent = 'Error';
                });
        </script>
    </body>
    </html>
    """)


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket.accept()
    monitor.websocket_connections.add(websocket)
    
    try:
        while True:
            # Keep connection alive and send periodic updates
            await asyncio.sleep(1)
            
            # Send system status
            status_data = {
                "type": "status",
                "timestamp": asyncio.get_event_loop().time(),
                "system_status": "running",
                "active_connections": len(monitor.websocket_connections)
            }
            await websocket.send_text(json.dumps(status_data))
            
    except WebSocketDisconnect:
        monitor.websocket_connections.discard(websocket)


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower()
    )
