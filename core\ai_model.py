"""
AI Model Manager for YOLOv8-based target detection
Handles model loading, inference, and result processing
"""
import torch
import cv2
import numpy as np
from ultralytics import YOL<PERSON>
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor

from config import settings


class DetectionResult:
    """Container for detection results"""
    
    def __init__(self, boxes: List[Dict], confidence: float, image_shape: Tuple[int, int]):
        self.boxes = boxes
        self.confidence = confidence
        self.image_shape = image_shape
        self.timestamp = asyncio.get_event_loop().time()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            "boxes": self.boxes,
            "confidence": self.confidence,
            "image_shape": self.image_shape,
            "timestamp": self.timestamp,
            "detection_count": len(self.boxes)
        }


class AIModelManager:
    """Manages the AI model for target detection"""
    
    def __init__(self):
        self.model: Optional[YOLO] = None
        self.device = settings.device
        self.confidence_threshold = settings.model_confidence
        self.iou_threshold = settings.model_iou_threshold
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.is_loaded = False
        
        # Class mappings for conveyor belt detection
        self.class_names = {
            0: "empty_belt",
            1: "cleared_belt", 
            2: "sku_present",
            3: "mixed_sku",
            4: "foreign_object"
        }
        
        # Risk levels for different detections
        self.risk_levels = {
            "empty_belt": "normal",
            "cleared_belt": "normal",
            "sku_present": "attention",
            "mixed_sku": "high",
            "foreign_object": "critical"
        }
    
    async def load_model(self, model_path: Optional[str] = None) -> bool:
        """Load the YOLO model asynchronously"""
        try:
            model_path = model_path or settings.model_path
            logger.info(f"Loading AI model from: {model_path}")
            
            # Check if model file exists
            if not Path(model_path).exists():
                logger.warning(f"Model file not found at {model_path}, downloading default YOLOv8n...")
                model_path = "yolov8n.pt"  # This will auto-download
            
            # Load model in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                self.executor, 
                self._load_model_sync, 
                model_path
            )
            
            self.is_loaded = True
            logger.info(f"Model loaded successfully on device: {self.device}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load model: {str(e)}")
            self.is_loaded = False
            return False
    
    def _load_model_sync(self, model_path: str) -> YOLO:
        """Synchronous model loading"""
        model = YOLO(model_path)
        
        # Set device
        if self.device == "cuda" and torch.cuda.is_available():
            model.to("cuda")
        elif self.device == "mps" and torch.backends.mps.is_available():
            model.to("mps")
        else:
            model.to("cpu")
            self.device = "cpu"
        
        return model
    
    async def detect(self, image: np.ndarray) -> DetectionResult:
        """Perform detection on an image"""
        if not self.is_loaded or self.model is None:
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        try:
            # Run inference in thread pool
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                self.executor,
                self._detect_sync,
                image
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Detection failed: {str(e)}")
            raise
    
    def _detect_sync(self, image: np.ndarray) -> DetectionResult:
        """Synchronous detection"""
        # Run inference
        results = self.model(
            image,
            conf=self.confidence_threshold,
            iou=self.iou_threshold,
            verbose=False
        )
        
        # Process results
        boxes = []
        max_confidence = 0.0
        
        if results and len(results) > 0:
            result = results[0]
            
            if result.boxes is not None:
                for box in result.boxes:
                    # Extract box coordinates
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    class_id = int(box.cls[0].cpu().numpy())
                    
                    # Map class ID to name
                    class_name = self.class_names.get(class_id, f"unknown_{class_id}")
                    risk_level = self.risk_levels.get(class_name, "unknown")
                    
                    box_data = {
                        "x1": float(x1),
                        "y1": float(y1),
                        "x2": float(x2),
                        "y2": float(y2),
                        "confidence": confidence,
                        "class_id": class_id,
                        "class_name": class_name,
                        "risk_level": risk_level,
                        "width": float(x2 - x1),
                        "height": float(y2 - y1)
                    }
                    
                    boxes.append(box_data)
                    max_confidence = max(max_confidence, confidence)
        
        return DetectionResult(
            boxes=boxes,
            confidence=max_confidence,
            image_shape=image.shape[:2]
        )
    
    async def detect_batch(self, images: List[np.ndarray]) -> List[DetectionResult]:
        """Perform batch detection on multiple images"""
        if not self.is_loaded or self.model is None:
            raise RuntimeError("Model not loaded. Call load_model() first.")
        
        tasks = [self.detect(image) for image in images]
        return await asyncio.gather(*tasks)
    
    def analyze_clearing_status(self, detection_result: DetectionResult) -> Dict[str, Any]:
        """Analyze the clearing status based on detection results"""
        analysis = {
            "status": "unknown",
            "risk_level": "normal",
            "issues": [],
            "recommendations": [],
            "clearing_percentage": 0.0
        }
        
        if not detection_result.boxes:
            analysis.update({
                "status": "empty",
                "risk_level": "normal",
                "clearing_percentage": 100.0,
                "recommendations": ["Belt appears clear - ready for next batch"]
            })
            return analysis
        
        # Analyze detected objects
        sku_present = False
        mixed_sku = False
        foreign_objects = False
        
        for box in detection_result.boxes:
            class_name = box["class_name"]
            confidence = box["confidence"]
            
            if class_name == "sku_present":
                sku_present = True
                analysis["issues"].append(f"SKU detected with {confidence:.1%} confidence")
            elif class_name == "mixed_sku":
                mixed_sku = True
                analysis["issues"].append(f"Mixed SKU detected with {confidence:.1%} confidence")
            elif class_name == "foreign_object":
                foreign_objects = True
                analysis["issues"].append(f"Foreign object detected with {confidence:.1%} confidence")
        
        # Determine status and risk level
        if foreign_objects:
            analysis.update({
                "status": "contaminated",
                "risk_level": "critical",
                "clearing_percentage": 0.0,
                "recommendations": [
                    "STOP PRODUCTION - Foreign object detected",
                    "Inspect and clean belt immediately",
                    "Verify object removal before resuming"
                ]
            })
        elif mixed_sku:
            analysis.update({
                "status": "mixed_sku",
                "risk_level": "high", 
                "clearing_percentage": 25.0,
                "recommendations": [
                    "Mixed SKU detected - potential quality issue",
                    "Verify SKU separation process",
                    "Check upstream sorting mechanisms"
                ]
            })
        elif sku_present:
            analysis.update({
                "status": "not_cleared",
                "risk_level": "attention",
                "clearing_percentage": 50.0,
                "recommendations": [
                    "Belt not fully cleared",
                    "Complete clearing process before next batch",
                    "Check clearing mechanism efficiency"
                ]
            })
        else:
            analysis.update({
                "status": "cleared",
                "risk_level": "normal",
                "clearing_percentage": 100.0,
                "recommendations": ["Belt cleared successfully - ready for production"]
            })
        
        return analysis
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        if not self.is_loaded or self.model is None:
            return {"loaded": False}
        
        return {
            "loaded": True,
            "device": self.device,
            "confidence_threshold": self.confidence_threshold,
            "iou_threshold": self.iou_threshold,
            "class_names": self.class_names,
            "model_type": "YOLOv8"
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.executor:
            self.executor.shutdown(wait=True)
        self.model = None
        self.is_loaded = False
        logger.info("AI Model Manager cleaned up")
