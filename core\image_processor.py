"""
Image Processing Pipeline for Conveyor Belt Monitoring
Handles image capture, preprocessing, and analysis
"""
import cv2
import numpy as np
from typing import Optional, Tuple, List, Dict, Any
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor
from loguru import logger
import base64
from io import BytesIO
from PIL import Image

from config import settings


class ImageProcessor:
    """Handles image processing operations for conveyor belt monitoring"""
    
    def __init__(self):
        self.target_size = (settings.image_width, settings.image_height)
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.camera = None
        self.is_camera_active = False
    
    async def initialize_camera(self, camera_index: Optional[int] = None) -> bool:
        """Initialize camera for live capture"""
        try:
            camera_index = camera_index or settings.camera_index
            logger.info(f"Initializing camera {camera_index}")
            
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                self.executor,
                self._init_camera_sync,
                camera_index
            )
            
            if success:
                self.is_camera_active = True
                logger.info("Camera initialized successfully")
            else:
                logger.error("Failed to initialize camera")
            
            return success
            
        except Exception as e:
            logger.error(f"Camera initialization error: {str(e)}")
            return False
    
    def _init_camera_sync(self, camera_index: int) -> bool:
        """Synchronous camera initialization"""
        try:
            self.camera = cv2.VideoCapture(camera_index)
            
            if not self.camera.isOpened():
                return False
            
            # Set camera properties
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, settings.camera_resolution[0])
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, settings.camera_resolution[1])
            self.camera.set(cv2.CAP_PROP_FPS, settings.camera_fps)
            
            # Test capture
            ret, frame = self.camera.read()
            return ret and frame is not None
            
        except Exception as e:
            logger.error(f"Camera sync init error: {str(e)}")
            return False
    
    async def capture_frame(self) -> Optional[np.ndarray]:
        """Capture a frame from the camera"""
        if not self.is_camera_active or self.camera is None:
            logger.warning("Camera not initialized")
            return None
        
        try:
            loop = asyncio.get_event_loop()
            frame = await loop.run_in_executor(
                self.executor,
                self._capture_frame_sync
            )
            return frame
            
        except Exception as e:
            logger.error(f"Frame capture error: {str(e)}")
            return None
    
    def _capture_frame_sync(self) -> Optional[np.ndarray]:
        """Synchronous frame capture"""
        try:
            ret, frame = self.camera.read()
            if ret and frame is not None:
                return frame
            return None
        except Exception as e:
            logger.error(f"Sync frame capture error: {str(e)}")
            return None
    
    async def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocess image for AI model inference"""
        try:
            loop = asyncio.get_event_loop()
            processed = await loop.run_in_executor(
                self.executor,
                self._preprocess_sync,
                image
            )
            return processed
            
        except Exception as e:
            logger.error(f"Image preprocessing error: {str(e)}")
            return image
    
    def _preprocess_sync(self, image: np.ndarray) -> np.ndarray:
        """Synchronous image preprocessing"""
        try:
            # Convert BGR to RGB if needed
            if len(image.shape) == 3 and image.shape[2] == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Resize to target size
            if image.shape[:2] != self.target_size:
                image = cv2.resize(image, self.target_size)
            
            # Normalize pixel values
            image = image.astype(np.float32) / 255.0
            
            # Apply histogram equalization for better contrast
            if len(image.shape) == 3:
                # Convert to LAB color space
                lab = cv2.cvtColor((image * 255).astype(np.uint8), cv2.COLOR_RGB2LAB)
                lab[:, :, 0] = cv2.equalizeHist(lab[:, :, 0])
                image = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB).astype(np.float32) / 255.0
            
            return image
            
        except Exception as e:
            logger.error(f"Sync preprocessing error: {str(e)}")
            return image
    
    async def load_image_from_file(self, file_path: str) -> Optional[np.ndarray]:
        """Load and preprocess image from file"""
        try:
            if not Path(file_path).exists():
                logger.error(f"Image file not found: {file_path}")
                return None
            
            loop = asyncio.get_event_loop()
            image = await loop.run_in_executor(
                self.executor,
                self._load_image_sync,
                file_path
            )
            
            if image is not None:
                return await self.preprocess_image(image)
            return None
            
        except Exception as e:
            logger.error(f"Image loading error: {str(e)}")
            return None
    
    def _load_image_sync(self, file_path: str) -> Optional[np.ndarray]:
        """Synchronous image loading"""
        try:
            image = cv2.imread(file_path)
            if image is not None:
                return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            return None
        except Exception as e:
            logger.error(f"Sync image loading error: {str(e)}")
            return None
    
    async def load_image_from_bytes(self, image_bytes: bytes) -> Optional[np.ndarray]:
        """Load image from bytes data"""
        try:
            loop = asyncio.get_event_loop()
            image = await loop.run_in_executor(
                self.executor,
                self._load_from_bytes_sync,
                image_bytes
            )
            
            if image is not None:
                return await self.preprocess_image(image)
            return None
            
        except Exception as e:
            logger.error(f"Image bytes loading error: {str(e)}")
            return None
    
    def _load_from_bytes_sync(self, image_bytes: bytes) -> Optional[np.ndarray]:
        """Synchronous image loading from bytes"""
        try:
            # Convert bytes to numpy array
            nparr = np.frombuffer(image_bytes, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is not None:
                return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            return None
            
        except Exception as e:
            logger.error(f"Sync bytes loading error: {str(e)}")
            return None
    
    def draw_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """Draw detection boxes and labels on image"""
        try:
            # Create a copy to avoid modifying original
            result_image = image.copy()
            
            # Convert to uint8 if needed
            if result_image.dtype == np.float32:
                result_image = (result_image * 255).astype(np.uint8)
            
            for detection in detections:
                x1, y1, x2, y2 = int(detection["x1"]), int(detection["y1"]), int(detection["x2"]), int(detection["y2"])
                confidence = detection["confidence"]
                class_name = detection["class_name"]
                risk_level = detection["risk_level"]
                
                # Choose color based on risk level
                color_map = {
                    "normal": (0, 255, 0),      # Green
                    "attention": (255, 255, 0),  # Yellow
                    "high": (255, 165, 0),       # Orange
                    "critical": (255, 0, 0)      # Red
                }
                color = color_map.get(risk_level, (128, 128, 128))
                
                # Draw bounding box
                cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)
                
                # Draw label
                label = f"{class_name}: {confidence:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                
                # Draw label background
                cv2.rectangle(
                    result_image,
                    (x1, y1 - label_size[1] - 10),
                    (x1 + label_size[0], y1),
                    color,
                    -1
                )
                
                # Draw label text
                cv2.putText(
                    result_image,
                    label,
                    (x1, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    (255, 255, 255),
                    2
                )
            
            return result_image
            
        except Exception as e:
            logger.error(f"Detection drawing error: {str(e)}")
            return image
    
    def image_to_base64(self, image: np.ndarray) -> str:
        """Convert image to base64 string for web display"""
        try:
            # Convert to uint8 if needed
            if image.dtype == np.float32:
                image = (image * 255).astype(np.uint8)
            
            # Convert to PIL Image
            pil_image = Image.fromarray(image)
            
            # Save to bytes buffer
            buffer = BytesIO()
            pil_image.save(buffer, format="JPEG", quality=85)
            
            # Encode to base64
            img_str = base64.b64encode(buffer.getvalue()).decode()
            return f"data:image/jpeg;base64,{img_str}"
            
        except Exception as e:
            logger.error(f"Base64 conversion error: {str(e)}")
            return ""
    
    async def save_image(self, image: np.ndarray, file_path: str) -> bool:
        """Save image to file"""
        try:
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                self.executor,
                self._save_image_sync,
                image,
                file_path
            )
            return success
            
        except Exception as e:
            logger.error(f"Image saving error: {str(e)}")
            return False
    
    def _save_image_sync(self, image: np.ndarray, file_path: str) -> bool:
        """Synchronous image saving"""
        try:
            # Convert to uint8 if needed
            if image.dtype == np.float32:
                image = (image * 255).astype(np.uint8)
            
            # Convert RGB to BGR for OpenCV
            if len(image.shape) == 3:
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            
            # Create directory if it doesn't exist
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Save image
            return cv2.imwrite(file_path, image)
            
        except Exception as e:
            logger.error(f"Sync image saving error: {str(e)}")
            return False
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.camera is not None:
            self.camera.release()
            self.camera = None
        
        if self.executor:
            self.executor.shutdown(wait=True)
        
        self.is_camera_active = False
        logger.info("Image Processor cleaned up")
